{"class_fqid": "com-snaplogic-pipeline_9", "snode_id": "66e82e8741d9609a8502b933", "instance_id": "d59b7191-a8bf-434e-90b8-b1ca607b0fa1", "instance_version": 492, "link_map": {"link511": {"dst_id": "136a04c9-0c7b-4c37-af4e-90ef52bc0360", "dst_view_id": "input0", "src_id": "9465a11f-b417-46d4-ad5c-c9e3bb901c3f", "src_view_id": "output0", "isGoto": false}, "link512": {"dst_id": "9465a11f-b417-46d4-ad5c-c9e3bb901c3f", "dst_view_id": "input1", "src_id": "e3ae297a-17e1-4d38-a12c-2c0ca4a8cd42", "src_view_id": "output101", "isGoto": false}, "link523": {"dst_id": "8657deb4-a14b-4121-b17a-c16a6fe0f25f", "dst_view_id": "input101", "src_id": "02633f83-e954-46b6-9b78-88cb5bfb6174", "src_view_id": "output1", "isGoto": false}, "link525": {"dst_id": "453cd1c0-17a6-41ec-baf2-02f5dcb8e7e4", "dst_view_id": "input0", "src_id": "46b84a2c-6dd8-4d1b-821e-d25e7df2f123", "src_view_id": "output0", "isGoto": false}, "link530": {"dst_id": "815bfa50-8a94-4e3c-8a63-fa2dd960d370", "dst_view_id": "input0", "src_id": "2984d891-9a01-4fce-88ce-db7d7139affd", "src_view_id": "output0", "isGoto": false}, "link564": {"dst_id": "ad1243d4-5b85-4dc2-80d4-bd23d93b5298", "dst_view_id": "input0", "src_id": "c82146e9-bb65-436a-a065-4ddd37a82507", "src_view_id": "output102", "isGoto": false}, "link572": {"dst_id": "2c42b9de-a028-45e0-a07c-77e13e6bd3bf", "dst_view_id": "input0", "src_id": "f3ffcb16-f003-4a60-84ed-4ad1fd6423fe", "src_view_id": "output103", "isGoto": false}, "link573": {"dst_id": "af33e159-dc0a-45b3-96d0-c0b032b812e2", "dst_view_id": "input0", "src_id": "2c42b9de-a028-45e0-a07c-77e13e6bd3bf", "src_view_id": "output0", "isGoto": false}, "link582": {"dst_id": "c82146e9-bb65-436a-a065-4ddd37a82507", "dst_view_id": "inputRows", "src_id": "9613ca88-f604-4e2c-ad92-68801d011a44", "src_view_id": "output0", "isGoto": false}, "link583": {"dst_id": "d79bc16b-5ead-4610-8006-1c81126748af", "dst_view_id": "input0", "src_id": "8ee6f782-9415-40d3-b026-1fb0d58ac028", "src_view_id": "output0", "isGoto": false}, "link584": {"dst_id": "8ee6f782-9415-40d3-b026-1fb0d58ac028", "dst_view_id": "input0", "src_id": "ad1243d4-5b85-4dc2-80d4-bd23d93b5298", "src_view_id": "output0", "isGoto": false}, "link585": {"dst_id": "08a58faa-582c-4fbf-a392-ce92594ac22a", "dst_view_id": "input0", "src_id": "8ee6f782-9415-40d3-b026-1fb0d58ac028", "src_view_id": "output1", "isGoto": false}, "link606": {"dst_id": "4e328d99-dd9b-4bae-a8b8-a78de23a211d", "dst_view_id": "input0", "src_id": "d45382f7-a63d-42e0-bcdc-f148b19fcbe1", "src_view_id": "output101", "isGoto": false}, "link607": {"dst_id": "d45382f7-a63d-42e0-bcdc-f148b19fcbe1", "dst_view_id": "input0", "src_id": "8ee6f782-9415-40d3-b026-1fb0d58ac028", "src_view_id": "output102", "isGoto": false}, "link609": {"dst_id": "304b5ccc-acea-4732-9656-5018e7140aca", "dst_view_id": "input0", "src_id": "af33e159-dc0a-45b3-96d0-c0b032b812e2", "src_view_id": "output0", "isGoto": false}, "link610": {"dst_id": "52cfe18a-d022-4ca5-8bc4-ee08cfac6a31", "dst_view_id": "input0", "src_id": "af33e159-dc0a-45b3-96d0-c0b032b812e2", "src_view_id": "output1", "isGoto": false}, "link613": {"dst_id": "2984d891-9a01-4fce-88ce-db7d7139affd", "dst_view_id": "input0", "src_id": "28dddefe-6abd-4081-a45e-8f65e188b44b", "src_view_id": "output0", "isGoto": false}, "link614": {"dst_id": "28dddefe-6abd-4081-a45e-8f65e188b44b", "dst_view_id": "input0", "src_id": "b4942172-e2fd-4b83-b219-e6c7a8133634", "src_view_id": "output0", "isGoto": false}, "link615": {"dst_id": "9613ca88-f604-4e2c-ad92-68801d011a44", "dst_view_id": "input0", "src_id": "b0f71648-c1fc-46d6-b64f-dcc44d44c633", "src_view_id": "output0", "isGoto": false}, "link616": {"dst_id": "b0f71648-c1fc-46d6-b64f-dcc44d44c633", "dst_view_id": "input0", "src_id": "2984d891-9a01-4fce-88ce-db7d7139affd", "src_view_id": "output1", "isGoto": false}, "link617": {"dst_id": "b4942172-e2fd-4b83-b219-e6c7a8133634", "dst_view_id": "input101", "src_id": "be52fd8a-f0fc-4c8f-8259-001ecedbb75c", "src_view_id": "output0", "isGoto": false}, "link619": {"dst_id": "b0f71648-c1fc-46d6-b64f-dcc44d44c633", "dst_view_id": "input1", "src_id": "be52fd8a-f0fc-4c8f-8259-001ecedbb75c", "src_view_id": "output1", "isGoto": true}, "link620": {"dst_id": "da9fdd93-0d1a-4442-89c4-ec920a0ca4f7", "dst_view_id": "input0", "src_id": "d3b08f27-c4b9-4956-b332-c9adc87e9c9d", "src_view_id": "output0", "isGoto": false}, "link621": {"dst_id": "d3b08f27-c4b9-4956-b332-c9adc87e9c9d", "dst_view_id": "input0", "src_id": "f7d66690-a04d-4401-86bf-24c1100dca76", "src_view_id": "output0", "isGoto": false}, "link622": {"dst_id": "f7d66690-a04d-4401-86bf-24c1100dca76", "dst_view_id": "input101", "src_id": "70a3bf8a-ce84-4860-8254-bb4ccb3dd514", "src_view_id": "output0", "isGoto": false}, "link625": {"dst_id": "dae1a6ec-fb55-457e-a321-10dd84c0fe2c", "dst_view_id": "input0", "src_id": "da9fdd93-0d1a-4442-89c4-ec920a0ca4f7", "src_view_id": "output0", "isGoto": false}, "link626": {"dst_id": "7ad7f835-6cf7-4e10-afc0-82e418868169", "dst_view_id": "input0", "src_id": "da9fdd93-0d1a-4442-89c4-ec920a0ca4f7", "src_view_id": "output1", "isGoto": false}, "link627": {"dst_id": "7ad7f835-6cf7-4e10-afc0-82e418868169", "dst_view_id": "input1", "src_id": "70a3bf8a-ce84-4860-8254-bb4ccb3dd514", "src_view_id": "output1", "isGoto": true}, "link628": {"dst_id": "93894540-7ecb-4a47-bca1-19aaa2b29f58", "dst_view_id": "input0", "src_id": "af33e159-dc0a-45b3-96d0-c0b032b812e2", "src_view_id": "output102", "isGoto": false}, "link629": {"dst_id": "f3ffcb16-f003-4a60-84ed-4ad1fd6423fe", "dst_view_id": "input0", "src_id": "bec92873-dab7-4248-812b-38f1204c09cd", "src_view_id": "output0", "isGoto": false}, "link630": {"dst_id": "bec92873-dab7-4248-812b-38f1204c09cd", "dst_view_id": "input0", "src_id": "7ad7f835-6cf7-4e10-afc0-82e418868169", "src_view_id": "output0", "isGoto": false}, "link643": {"dst_id": "9055d232-09f4-40e5-be0c-ff9d92007815", "dst_view_id": "input0", "src_id": "453cd1c0-17a6-41ec-baf2-02f5dcb8e7e4", "src_view_id": "output0", "isGoto": false}, "link649": {"dst_id": "699fde6c-f9f6-4958-8af2-be86f62821be", "dst_view_id": "input0", "src_id": "af1f3ce6-8f73-4633-8800-64ad248f40b4", "src_view_id": "output0", "isGoto": false}, "link650": {"dst_id": "4abdf61a-82db-4b5f-b9b0-69bf06c29157", "dst_view_id": "input0", "src_id": "9055d232-09f4-40e5-be0c-ff9d92007815", "src_view_id": "output0", "isGoto": false}, "link651": {"dst_id": "46b84a2c-6dd8-4d1b-821e-d25e7df2f123", "dst_view_id": "input101", "src_id": "02633f83-e954-46b6-9b78-88cb5bfb6174", "src_view_id": "output0", "isGoto": false}, "link652": {"dst_id": "02633f83-e954-46b6-9b78-88cb5bfb6174", "dst_view_id": "input0", "src_id": "47d7b8ff-6a0b-48e3-9fba-0604673affaf", "src_view_id": "output101", "isGoto": false}, "link653": {"dst_id": "47d7b8ff-6a0b-48e3-9fba-0604673affaf", "dst_view_id": "input102", "src_id": "d1f6c47c-d103-49ee-9f9e-ff46588b65d5", "src_view_id": "output0", "isGoto": false}, "link657": {"dst_id": "6a184be3-1171-43c6-9a79-22459736e3cf", "dst_view_id": "input0", "src_id": "4abdf61a-82db-4b5f-b9b0-69bf06c29157", "src_view_id": "output0", "isGoto": false}, "link658": {"dst_id": "be52fd8a-f0fc-4c8f-8259-001ecedbb75c", "dst_view_id": "input0", "src_id": "6a184be3-1171-43c6-9a79-22459736e3cf", "src_view_id": "output0", "isGoto": false}, "link659": {"dst_id": "70a3bf8a-ce84-4860-8254-bb4ccb3dd514", "dst_view_id": "input0", "src_id": "136a04c9-0c7b-4c37-af4e-90ef52bc0360", "src_view_id": "output0", "isGoto": false}, "link668": {"dst_id": "af1f3ce6-8f73-4633-8800-64ad248f40b4", "dst_view_id": "input0", "src_id": "8657deb4-a14b-4121-b17a-c16a6fe0f25f", "src_view_id": "output0", "isGoto": false}, "link671": {"dst_id": "9465a11f-b417-46d4-ad5c-c9e3bb901c3f", "dst_view_id": "input0", "src_id": "699fde6c-f9f6-4958-8af2-be86f62821be", "src_view_id": "output0", "isGoto": false}, "link672": {"dst_id": "1c01c949-c8dd-4709-9614-ef233b76fac7", "dst_view_id": "input0", "src_id": "af33e159-dc0a-45b3-96d0-c0b032b812e2", "src_view_id": "output104", "isGoto": false}}, "link_serial": 677, "property_map": {"info": {"label": {"value": "P01 - Property CRM365 to <PERSON><PERSON><PERSON>"}, "author": {"value": "<EMAIL>"}, "pipeline_doc_uri": {"value": null}, "notes": {"value": null}, "purpose": {"value": null}}, "error": {"error_behavior": {"value": "none"}}, "settings": {"error_pipeline": {"value": "Error Writer", "expression": false}, "error_param_table": {"value": []}, "param_table": {"value": [{"capture": {"value": false}, "key": {"value": "propertyGuid"}, "value": {"value": "CF7153E2-3BB7-EC11-80F2-00155D00B75D"}, "required": {"value": false}, "data_type": {"value": "string"}, "description": {"value": null}}, {"capture": {"value": false}, "key": {"value": "unitCode"}, "value": {"value": "11000999"}, "required": {"value": false}, "data_type": {"value": "string"}, "description": {"value": null}}]}, "imports": {"value": []}}, "input": {"d1f6c47c-d103-49ee-9f9e-ff46588b65d5_input0": {"view_type": {"value": "document"}, "label": {"value": "Map UnitCode Parameter - input0"}}}, "output": {"af33e159-dc0a-45b3-96d0-c0b032b812e2_output104": {"label": {"value": "Copy - output3"}, "view_type": {"value": "document"}}}}, "render_map": {"scale_ratio": 1, "pan_x_num": 0, "pan_y_num": 0, "default_snaplex": "5fe335e211b4d4884c3302d9", "detail_map": {"d1f6c47c-d103-49ee-9f9e-ff46588b65d5": {"grid_x_int": 1, "grid_y_int": 2, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0}, "02633f83-e954-46b6-9b78-88cb5bfb6174": {"grid_x_int": 3, "grid_y_int": 2, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {"output1": {"rot_int": 0, "dx_int": 0, "dy_int": 1}}}, "f3ffcb16-f003-4a60-84ed-4ad1fd6423fe": {"grid_x_int": 13, "grid_y_int": 6, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0}, "136a04c9-0c7b-4c37-af4e-90ef52bc0360": {"grid_x_int": 6, "grid_y_int": 5, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0}, "9465a11f-b417-46d4-ad5c-c9e3bb901c3f": {"grid_x_int": 5, "grid_y_int": 5, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "input": {"input1": {"rot_int": 0, "dx_int": 0, "dy_int": 1}}}, "e3ae297a-17e1-4d38-a12c-2c0ca4a8cd42": {"grid_x_int": 4, "grid_y_int": 6, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "input": {}}, "af1f3ce6-8f73-4633-8800-64ad248f40b4": {"grid_x_int": 4, "grid_y_int": 4, "rot_int": 90, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 90}, "8657deb4-a14b-4121-b17a-c16a6fe0f25f": {"grid_x_int": 4, "grid_y_int": 3, "rot_int": 90, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0}, "46b84a2c-6dd8-4d1b-821e-d25e7df2f123": {"grid_x_int": 4, "grid_y_int": 2, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0}, "453cd1c0-17a6-41ec-baf2-02f5dcb8e7e4": {"grid_x_int": 5, "grid_y_int": 2, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0}, "4abdf61a-82db-4b5f-b9b0-69bf06c29157": {"grid_x_int": 7, "grid_y_int": 2, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0}, "6a184be3-1171-43c6-9a79-22459736e3cf": {"grid_x_int": 8, "grid_y_int": 2, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0}, "2984d891-9a01-4fce-88ce-db7d7139affd": {"grid_x_int": 12, "grid_y_int": 2, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {"output1": {"rot_int": 0, "dx_int": 0, "dy_int": 1}}}, "815bfa50-8a94-4e3c-8a63-fa2dd960d370": {"grid_x_int": 13, "grid_y_int": 2, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {}}, "9613ca88-f604-4e2c-ad92-68801d011a44": {"grid_x_int": 14, "grid_y_int": 3, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0}, "c82146e9-bb65-436a-a065-4ddd37a82507": {"grid_x_int": 15, "grid_y_int": 3, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0}, "d79bc16b-5ead-4610-8006-1c81126748af": {"grid_x_int": 18, "grid_y_int": 3, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {}}, "08a58faa-582c-4fbf-a392-ce92594ac22a": {"grid_x_int": 18, "grid_y_int": 4, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {}}, "af33e159-dc0a-45b3-96d0-c0b032b812e2": {"grid_x_int": 15, "grid_y_int": 6, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {"output1": {"rot_int": 0, "dx_int": 0, "dy_int": 1}, "output102": {"rot_int": 0, "dx_int": 0, "dy_int": 2}, "output104": {"rot_int": 0, "dx_int": 0, "dy_int": 3}}}, "52cfe18a-d022-4ca5-8bc4-ee08cfac6a31": {"grid_x_int": 16, "grid_y_int": 7, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {}}, "304b5ccc-acea-4732-9656-5018e7140aca": {"grid_x_int": 16, "grid_y_int": 6, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {}}, "93894540-7ecb-4a47-bca1-19aaa2b29f58": {"grid_x_int": 16, "grid_y_int": 8, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {}}, "ad1243d4-5b85-4dc2-80d4-bd23d93b5298": {"grid_x_int": 16, "grid_y_int": 3, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0}, "4e328d99-dd9b-4bae-a8b8-a78de23a211d": {"grid_x_int": 19, "grid_y_int": 5, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {}}, "2c42b9de-a028-45e0-a07c-77e13e6bd3bf": {"grid_x_int": 14, "grid_y_int": 6, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0}, "8ee6f782-9415-40d3-b026-1fb0d58ac028": {"grid_x_int": 17, "grid_y_int": 3, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {"output1": {"rot_int": 0, "dx_int": 0, "dy_int": 1}, "output102": {"rot_int": 0, "dx_int": 0, "dy_int": 2}}}, "d45382f7-a63d-42e0-bcdc-f148b19fcbe1": {"grid_x_int": 18, "grid_y_int": 5, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0}, "b4942172-e2fd-4b83-b219-e6c7a8133634": {"grid_x_int": 10, "grid_y_int": 2, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0}, "28dddefe-6abd-4081-a45e-8f65e188b44b": {"grid_x_int": 11, "grid_y_int": 2, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0}, "b0f71648-c1fc-46d6-b64f-dcc44d44c633": {"grid_x_int": 13, "grid_y_int": 3, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {}, "input": {"input1": {"rot_int": 0, "dx_int": 0, "dy_int": 1}}}, "be52fd8a-f0fc-4c8f-8259-001ecedbb75c": {"grid_x_int": 9, "grid_y_int": 2, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {"output1": {"rot_int": 0, "dx_int": 0, "dy_int": 1}}}, "70a3bf8a-ce84-4860-8254-bb4ccb3dd514": {"grid_x_int": 7, "grid_y_int": 5, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {"output1": {"rot_int": 0, "dx_int": 0, "dy_int": 1}}}, "f7d66690-a04d-4401-86bf-24c1100dca76": {"grid_x_int": 8, "grid_y_int": 5, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0}, "d3b08f27-c4b9-4956-b332-c9adc87e9c9d": {"grid_x_int": 9, "grid_y_int": 5, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0}, "da9fdd93-0d1a-4442-89c4-ec920a0ca4f7": {"grid_x_int": 10, "grid_y_int": 5, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {"output1": {"rot_int": 0, "dx_int": 0, "dy_int": 1}}}, "7ad7f835-6cf7-4e10-afc0-82e418868169": {"grid_x_int": 11, "grid_y_int": 6, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {}, "input": {"input1": {"rot_int": 0, "dx_int": 0, "dy_int": 1}}}, "dae1a6ec-fb55-457e-a321-10dd84c0fe2c": {"grid_x_int": 11, "grid_y_int": 5, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0, "output": {}}, "bec92873-dab7-4248-812b-38f1204c09cd": {"grid_x_int": 12, "grid_y_int": 6, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0}, "9055d232-09f4-40e5-be0c-ff9d92007815": {"grid_x_int": 6, "grid_y_int": 2, "rot_int": 0, "recommendation_id": null, "source": "snap catagory", "index": null, "rot_tail_int": 0}, "699fde6c-f9f6-4958-8af2-be86f62821be": {"grid_x_int": 4, "grid_y_int": 5, "rot_int": 0, "recommendation_id": null, "source": "snap catagory", "index": null, "rot_tail_int": 90}, "47d7b8ff-6a0b-48e3-9fba-0604673affaf": {"grid_x_int": 2, "grid_y_int": 2, "rot_int": 0, "recommendation_id": null, "source": "", "index": null, "rot_tail_int": 0}, "1c01c949-c8dd-4709-9614-ef233b76fac7": {"grid_x_int": 16, "grid_y_int": 9, "rot_int": 0, "recommendation_id": null, "source": "snap catagory", "index": null, "rot_tail_int": 0}}}, "snap_map": {"d1f6c47c-d103-49ee-9f9e-ff46588b65d5": {"class_fqid": "com-snaplogic-snaps-transform-datatransform_4-main22460", "class_id": "com-snaplogic-snaps-transform-datatransform", "class_version": 4, "instance_fqid": "d1f6c47c-d103-49ee-9f9e-ff46588b65d5_1", "instance_id": "d1f6c47c-d103-49ee-9f9e-ff46588b65d5", "instance_version": 1, "property_map": {"info": {"label": {"value": "Map UnitCode Parameter"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "passThrough": {"value": false}, "transformations": {"value": {"mappingRoot": {"value": "$"}, "mappingTable": {"value": [{"expression": {"value": "_unitCode", "expression": true}, "targetPath": {"value": "$UnitCode"}}]}}}, "nullSafeAccess": {"value": true}}, "view_serial": 100}, "class_build_tag": "main22460"}, "02633f83-e954-46b6-9b78-88cb5bfb6174": {"class_fqid": "com-snaplogic-snaps-flow-router_2-433patches21196", "class_id": "com-snaplogic-snaps-flow-router", "class_version": 2, "instance_fqid": "02633f83-e954-46b6-9b78-88cb5bfb6174_1", "instance_id": "02633f83-e954-46b6-9b78-88cb5bfb6174", "instance_version": 1, "property_map": {"info": {"label": {"value": "Route propExists "}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "Insert"}}, "output1": {"view_type": {"value": "document"}, "label": {"value": "Update"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "routes": {"value": [{"expression": {"value": "$propExists == 0", "expression": true}, "outputViewName": {"value": "Insert"}}, {"expression": {"value": "$propExists > 0", "expression": true}, "outputViewName": {"value": "Update"}}]}, "firstMatch": {"value": true}}, "view_serial": 100, "account": {"account_ref": {"value": {"label": {"value": "KYP Test - SA", "label": "../../shared/KYP Test - SA", "expression": false}, "ref_class_id": {"value": "com-snaplogic-snap-api-sql-accounts-sqlserverdatabaseaccount"}, "ref_id": {"value": "e986ae44-ec39-4117-b4db-5888e214a06e", "expression": false}}}}}, "class_build_tag": "433patches21196"}, "f3ffcb16-f003-4a60-84ed-4ad1fd6423fe": {"class_fqid": "com-snaplogic-snaps-sqlserver-update_1-main21015", "class_id": "com-snaplogic-snaps-sqlserver-update", "class_version": 1, "instance_fqid": "f3ffcb16-f003-4a60-84ed-4ad1fd6423fe_1", "instance_id": "f3ffcb16-f003-4a60-84ed-4ad1fd6423fe", "instance_version": 1, "property_map": {"info": {"label": {"value": "Update Property"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "executable_during_suggest": {"value": false}, "maxRetryProp": {"value": 0, "expression": false}, "retryIntervalProp": {"value": 1, "expression": false}, "schemaName": {"value": "dbo", "expression": false}, "tableName": {"value": "PropertyStructure_Unit", "expression": false}, "updateCondition": {"value": "\"ID='\" + $ID+ \"'\"", "expression": true}}, "account": {"account_ref": {"value": {"label": {"value": "KYPERA (Live)", "label": "../shared/KYPERA (Live)", "expression": false}, "ref_class_id": {"value": "com-snaplogic-snap-api-sql-accounts-sqlserverdatabaseaccount"}, "ref_id": {"value": "d1999d56-4b1a-4984-a906-b398bfdae8ea", "expression": false}}}}, "view_serial": 103, "output": {"output103": {"label": {"value": "output0"}, "view_type": {"value": "document"}}}}, "class_build_tag": "main21015"}, "136a04c9-0c7b-4c37-af4e-90ef52bc0360": {"class_fqid": "com-snaplogic-snaps-transform-datatransform_4-main22460", "class_id": "com-snaplogic-snaps-transform-datatransform", "class_version": 4, "instance_fqid": "136a04c9-0c7b-4c37-af4e-90ef52bc0360_2", "instance_id": "136a04c9-0c7b-4c37-af4e-90ef52bc0360", "instance_version": 2, "property_map": {"info": {"label": {"value": "Map Kypera StreetCode"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "passThrough": {"value": false}, "transformations": {"value": {"mappingRoot": {"value": "$"}, "mappingTable": {"value": [{"expression": {"value": "$Company", "expression": true}, "targetPath": {"value": "$Company"}}, {"expression": {"value": "$ID", "expression": true}, "targetPath": {"value": "$ID"}}, {"expression": {"value": "$Name", "expression": true}, "targetPath": {"value": "$Name"}}, {"expression": {"value": "$Number", "expression": true}, "targetPath": {"value": "$Number"}}, {"expression": {"value": "$BlockTypeId", "expression": true}, "targetPath": {"value": "$BlockTypeId"}}, {"expression": {"value": "$BuildingName", "expression": true}, "targetPath": {"value": "$BuildingName"}}, {"expression": {"value": "$input1_Street", "expression": true}, "targetPath": {"value": "$Street"}}, {"expression": {"value": "$input1_StreetCode", "expression": true}, "targetPath": {"value": "$StreetCode"}}, {"expression": {"value": "$Town", "expression": true}, "targetPath": {"value": "$Town"}}, {"expression": {"value": "$Postcode", "expression": true}, "targetPath": {"value": "$Postcode"}}, {"expression": {"value": "$PropertyType", "expression": true}, "targetPath": {"value": "$PropertyType"}}, {"expression": {"value": "$PropertySubType", "expression": true}, "targetPath": {"value": "$PropertySubType"}}, {"expression": {"value": "$IsGarageOrLockup", "expression": true}, "targetPath": {"value": "$IsGarageOrLockup"}}, {"expression": {"value": "$LastUpdateBy", "expression": true}, "targetPath": {"value": "$LastUpdateBy"}}, {"expression": {"value": "$LastUpdateOn", "expression": true}, "targetPath": {"value": "$LastUpdateOn"}}, {"expression": {"value": "$DefaultBlock", "expression": true}, "targetPath": {"value": "$DefaultBlock"}}, {"expression": {"value": "$Aquired", "expression": true}, "targetPath": {"value": "$Aquired"}}, {"expression": {"value": "$Address", "expression": true}, "targetPath": {"value": "$Address", "expression": true}}, {"expression": {"value": "$AlternateReference", "expression": true}, "targetPath": {"value": "$AlternateReference", "expression": true}}, {"expression": {"value": "$AreaOffice", "expression": true}, "targetPath": {"value": "$AreaOffice", "expression": true}}, {"expression": {"value": "$AsbestosDetails", "expression": true}, "targetPath": {"value": "$AsbestosDetails", "expression": true}}, {"expression": {"value": "$AsbestosMaterialAssessment", "expression": true}, "targetPath": {"value": "$AsbestosMaterialAssessment", "expression": true}}, {"expression": {"value": "$AsbestosPresent", "expression": true}, "targetPath": {"value": "$AsbestosPresent", "expression": true}}, {"expression": {"value": "$AsbestosPriorityAssessment", "expression": true}, "targetPath": {"value": "$AsbestosPriorityAssessment", "expression": true}}, {"expression": {"value": "$AsbestosSurveyDate", "expression": true}, "targetPath": {"value": "$AsbestosSurveyDate", "expression": true}}, {"expression": {"value": "$AsbestosTotalRisk", "expression": true}, "targetPath": {"value": "$AsbestosTotalRisk", "expression": true}}, {"expression": {"value": "$Association", "expression": true}, "targetPath": {"value": "$Association", "expression": true}}, {"expression": {"value": "$BuildYear", "expression": true}, "targetPath": {"value": "$BuildYear", "expression": true}}, {"expression": {"value": "$BuildYearEstFrom", "expression": true}, "targetPath": {"value": "$BuildYearEstFrom", "expression": true}}, {"expression": {"value": "$BuildYearEstTo", "expression": true}, "targetPath": {"value": "$BuildYearEstTo", "expression": true}}, {"expression": {"value": "$BuildYearPrePost", "expression": true}, "targetPath": {"value": "$BuildYearPrePost", "expression": true}}, {"expression": {"value": "$CAHProgram", "expression": true}, "targetPath": {"value": "$CAHProgram", "expression": true}}, {"expression": {"value": "$CaseManagementAgency", "expression": true}, "targetPath": {"value": "$CaseManagementAgency", "expression": true}}, {"expression": {"value": "$CeilingRent", "expression": true}, "targetPath": {"value": "$CeilingRent", "expression": true}}, {"expression": {"value": "$ChargeGroupFactored", "expression": true}, "targetPath": {"value": "$ChargeGroupFactored", "expression": true}}, {"expression": {"value": "$Commercial", "expression": true}, "targetPath": {"value": "$Commercial", "expression": true}}, {"expression": {"value": "$CommercialUnits", "expression": true}, "targetPath": {"value": "$CommercialUnits", "expression": true}}, {"expression": {"value": "$CommonArea", "expression": true}, "targetPath": {"value": "$CommonArea", "expression": true}}, {"expression": {"value": "$ConditionCode", "expression": true}, "targetPath": {"value": "$ConditionCode", "expression": true}}, {"expression": {"value": "$ContiguousBoundaryIsPrimary", "expression": true}, "targetPath": {"value": "$ContiguousBoundaryIsPrimary", "expression": true}}, {"expression": {"value": "$ContiguousBoundaryPrimaryUnitID", "expression": true}, "targetPath": {"value": "$ContiguousBoundaryPrimaryUnitID", "expression": true}}, {"expression": {"value": "$CouncilRates", "expression": true}, "targetPath": {"value": "$CouncilRates", "expression": true}}, {"expression": {"value": "$CouncilTaxBand", "expression": true}, "targetPath": {"value": "$CouncilTaxBand", "expression": true}}, {"expression": {"value": "$County", "expression": true}, "targetPath": {"value": "$County", "expression": true}}, {"expression": {"value": "$CountyID", "expression": true}, "targetPath": {"value": "$CountyID", "expression": true}}, {"expression": {"value": "$CPGPreOrPostReform", "expression": true}, "targetPath": {"value": "$CPGPreOrPostReform", "expression": true}}, {"expression": {"value": "$CreatedBy", "expression": true}, "targetPath": {"value": "$CreatedBy", "expression": true}}, {"expression": {"value": "$CreatedOn", "expression": true}, "targetPath": {"value": "$CreatedOn", "expression": true}}, {"expression": {"value": "$DateRelinquished", "expression": true}, "targetPath": {"value": "$DateRelinquished", "expression": true}}, {"expression": {"value": "$DecentHomeIndicator", "expression": true}, "targetPath": {"value": "$DecentHomeIndicator", "expression": true}}, {"expression": {"value": "$DecentHomeSurveyGeneratedDate", "expression": true}, "targetPath": {"value": "$DecentHomeSurveyGeneratedDate", "expression": true}}, {"expression": {"value": "$Demand", "expression": true}, "targetPath": {"value": "$Demand", "expression": true}}, {"expression": {"value": "$DemolishedDate", "expression": true}, "targetPath": {"value": "$DemolishedDate", "expression": true}}, {"expression": {"value": "$DisabilityModifications", "expression": true}, "targetPath": {"value": "$DisabilityModifications", "expression": true}}, {"expression": {"value": "$DwellingCode", "expression": true}, "targetPath": {"value": "$DwellingCode", "expression": true}}, {"expression": {"value": "$ExcludeFromDebitRun", "expression": true}, "targetPath": {"value": "$ExcludeFromDebitRun", "expression": true}}, {"expression": {"value": "$ExternalFilesFolder", "expression": true}, "targetPath": {"value": "$ExternalFilesFolder", "expression": true}}, {"expression": {"value": "$FactoredType", "expression": true}, "targetPath": {"value": "$FactoredType", "expression": true}}, {"expression": {"value": "$FinanceOfficer", "expression": true}, "targetPath": {"value": "$FinanceOfficer", "expression": true}}, {"expression": {"value": "$FinancialAnalysis1", "expression": true}, "targetPath": {"value": "$FinancialAnalysis1", "expression": true}}, {"expression": {"value": "$FinancialAnalysis2", "expression": true}, "targetPath": {"value": "$FinancialAnalysis2", "expression": true}}, {"expression": {"value": "$Flat", "expression": true}, "targetPath": {"value": "$Flat", "expression": true}}, {"expression": {"value": "$Funder", "expression": true}, "targetPath": {"value": "$Funder", "expression": true}}, {"expression": {"value": "$GroupHouseholdProperty", "expression": true}, "targetPath": {"value": "$GroupHouseholdProperty", "expression": true}}, {"expression": {"value": "$HouseholdUnits", "expression": true}, "targetPath": {"value": "$HouseholdUnits", "expression": true}}, {"expression": {"value": "$isDemolished", "expression": true}, "targetPath": {"value": "$isDemolished", "expression": true}}, {"expression": {"value": "$isRelinquished", "expression": true}, "targetPath": {"value": "$isRelinquished", "expression": true}}, {"expression": {"value": "$isShared", "expression": true}, "targetPath": {"value": "$isShared", "expression": true}}, {"expression": {"value": "$isSold", "expression": true}, "targetPath": {"value": "$isSold", "expression": true}}, {"expression": {"value": "$KeysetsAvailable", "expression": true}, "targetPath": {"value": "$KeysetsAvailable", "expression": true}}, {"expression": {"value": "$LandLordAddress", "expression": true}, "targetPath": {"value": "$LandLordAddress", "expression": true}}, {"expression": {"value": "$LandLordContactForename", "expression": true}, "targetPath": {"value": "$LandLordContactForename", "expression": true}}, {"expression": {"value": "$LandLordContactSurname", "expression": true}, "targetPath": {"value": "$LandLordContactSurname", "expression": true}}, {"expression": {"value": "$LandLordContactTitle", "expression": true}, "targetPath": {"value": "$LandLordContactTitle", "expression": true}}, {"expression": {"value": "$LandLordEmailAddress", "expression": true}, "targetPath": {"value": "$LandLordEmailAddress", "expression": true}}, {"expression": {"value": "$LandLordEndDate", "expression": true}, "targetPath": {"value": "$LandLordEndDate", "expression": true}}, {"expression": {"value": "$LandLordExtended", "expression": true}, "targetPath": {"value": "$LandLordExtended", "expression": true}}, {"expression": {"value": "$LandLordExtendedDate", "expression": true}, "targetPath": {"value": "$LandLordExtendedDate", "expression": true}}, {"expression": {"value": "$LandLordFaxNumber", "expression": true}, "targetPath": {"value": "$LandLordFaxNumber", "expression": true}}, {"expression": {"value": "$LandLordMobileNumber", "expression": true}, "targetPath": {"value": "$LandLordMobileNumber", "expression": true}}, {"expression": {"value": "$LandLordName", "expression": true}, "targetPath": {"value": "$LandLordName", "expression": true}}, {"expression": {"value": "$LandLordNotes", "expression": true}, "targetPath": {"value": "$LandLordNotes", "expression": true}}, {"expression": {"value": "$LandLordPostCode", "expression": true}, "targetPath": {"value": "$LandLordPostCode", "expression": true}}, {"expression": {"value": "$LandLordReference", "expression": true}, "targetPath": {"value": "$LandLordReference", "expression": true}}, {"expression": {"value": "$LandLordStartDate", "expression": true}, "targetPath": {"value": "$LandLordStartDate", "expression": true}}, {"expression": {"value": "$LandLordTelephoneNumber", "expression": true}, "targetPath": {"value": "$LandLordTelephoneNumber", "expression": true}}, {"expression": {"value": "$LandLordTerminationDate", "expression": true}, "targetPath": {"value": "$LandLordTerminationDate", "expression": true}}, {"expression": {"value": "$LastMainRentIncrease", "expression": true}, "targetPath": {"value": "$LastMainRentIncrease", "expression": true}}, {"expression": {"value": "$LastOccupancyType", "expression": true}, "targetPath": {"value": "$LastOccupancyType", "expression": true}}, {"expression": {"value": "$Latitude", "expression": true}, "targetPath": {"value": "$Latitude", "expression": true}}, {"expression": {"value": "$LocalAuthorityArea", "expression": true}, "targetPath": {"value": "$LocalAuthorityArea", "expression": true}}, {"expression": {"value": "$LocationOfMainsWaterIsolationValve", "expression": true}, "targetPath": {"value": "$LocationOfMainsWaterIsolationValve", "expression": true}}, {"expression": {"value": "$Longitude", "expression": true}, "targetPath": {"value": "$Longitude", "expression": true}}, {"expression": {"value": "$MarketRent", "expression": true}, "targetPath": {"value": "$MarketRent", "expression": true}}, {"expression": {"value": "$MarketRentMethod", "expression": true}, "targetPath": {"value": "$MarketRentMethod", "expression": true}}, {"expression": {"value": "$NextMainRentIncrease", "expression": true}, "targetPath": {"value": "$NextMainRentIncrease", "expression": true}}, {"expression": {"value": "$NominalCompany", "expression": true}, "targetPath": {"value": "$NominalCompany", "expression": true}}, {"expression": {"value": "$NominalCostCentre", "expression": true}, "targetPath": {"value": "$NominalCostCentre", "expression": true}}, {"expression": {"value": "$NominalDepartment", "expression": true}, "targetPath": {"value": "$NominalDepartment", "expression": true}}, {"expression": {"value": "$NominationRights", "expression": true}, "targetPath": {"value": "$NominationRights", "expression": true}}, {"expression": {"value": "$NonHousingUnitType", "expression": true}, "targetPath": {"value": "$NonHousingUnitType", "expression": true}}, {"expression": {"value": "$NRSCHAssetClass", "expression": true}, "targetPath": {"value": "$NRSCHAssetClass", "expression": true}}, {"expression": {"value": "$NRSCHHousingAgencnyIdentifier", "expression": true}, "targetPath": {"value": "$NRSCHHousingAgencnyIdentifier", "expression": true}}, {"expression": {"value": "$NRSCHMaintenanceLiability", "expression": true}, "targetPath": {"value": "$NRSCHMaintenanceLiability", "expression": true}}, {"expression": {"value": "$NRSCHManagedByOther", "expression": true}, "targetPath": {"value": "$NRSCHManagedByOther", "expression": true}}, {"expression": {"value": "$NRSCHManagingAgency", "expression": true}, "targetPath": {"value": "$NRSCHManagingAgency", "expression": true}}, {"expression": {"value": "$NRSCHRegisteredOwner", "expression": true}, "targetPath": {"value": "$NRSCHRegisteredOwner", "expression": true}}, {"expression": {"value": "$NRSCHStateHoldsInterest", "expression": true}, "targetPath": {"value": "$NRSCHStateHoldsInterest", "expression": true}}, {"expression": {"value": "$NRSCHStateVestedInterest", "expression": true}, "targetPath": {"value": "$NRSCHStateVestedInterest", "expression": true}}, {"expression": {"value": "$OptedOut", "expression": true}, "targetPath": {"value": "$OptedOut", "expression": true}}, {"expression": {"value": "$Ownership", "expression": true}, "targetPath": {"value": "$Ownership", "expression": true}}, {"expression": {"value": "$OwningBody", "expression": true}, "targetPath": {"value": "$OwningBody", "expression": true}}, {"expression": {"value": "$PercentageOwnedByAssocation", "expression": true}, "targetPath": {"value": "$PercentageOwnedByAssocation", "expression": true}}, {"expression": {"value": "$Pre", "expression": true}, "targetPath": {"value": "$Pre", "expression": true}}, {"expression": {"value": "$PreventChargeIncreasesUntil", "expression": true}, "targetPath": {"value": "$PreventChargeIncreasesUntil", "expression": true}}, {"expression": {"value": "$PreventRentIncreasesUntil", "expression": true}, "targetPath": {"value": "$PreventRentIncreasesUntil", "expression": true}}, {"expression": {"value": "$PropertySource", "expression": true}, "targetPath": {"value": "$PropertySource", "expression": true}}, {"expression": {"value": "$PropertyStyle", "expression": true}, "targetPath": {"value": "$PropertyStyle", "expression": true}}, {"expression": {"value": "$ReasonForSale", "expression": true}, "targetPath": {"value": "$ReasonForSale", "expression": true}}, {"expression": {"value": "$RentRegime", "expression": true}, "targetPath": {"value": "$RentRegime", "expression": true}}, {"expression": {"value": "$RentRegimeEffectiveDate", "expression": true}, "targetPath": {"value": "$RentRegimeEffectiveDate", "expression": true}}, {"expression": {"value": "$RentSettingBedroomWeighting", "expression": true}, "targetPath": {"value": "$RentSettingBedroomWeighting", "expression": true}}, {"expression": {"value": "$RentSettingUnitValue", "expression": true}, "targetPath": {"value": "$RentSettingUnitValue", "expression": true}}, {"expression": {"value": "$RepairsOnHold", "expression": true}, "targetPath": {"value": "$RepairsOnHold", "expression": true}}, {"expression": {"value": "$RTBAppliedFor", "expression": true}, "targetPath": {"value": "$RTBAppliedFor", "expression": true}}, {"expression": {"value": "$RtbRights", "expression": true}, "targetPath": {"value": "$RtbRights", "expression": true}}, {"expression": {"value": "$SAPRating", "expression": true}, "targetPath": {"value": "$SAPRating", "expression": true}}, {"expression": {"value": "$SharedOwnershipTranche", "expression": true}, "targetPath": {"value": "$SharedOwnershipTranche", "expression": true}}, {"expression": {"value": "$SHQS", "expression": true}, "targetPath": {"value": "$SHQS", "expression": true}}, {"expression": {"value": "$ShqsConformanceDate", "expression": true}, "targetPath": {"value": "$ShqsConformanceDate", "expression": true}}, {"expression": {"value": "$ShqsNonConformanceReason", "expression": true}, "targetPath": {"value": "$ShqsNonConformanceReason", "expression": true}}, {"expression": {"value": "$ShqsStatus", "expression": true}, "targetPath": {"value": "$ShqsStatus", "expression": true}}, {"expression": {"value": "$SoldDate", "expression": true}, "targetPath": {"value": "$SoldDate", "expression": true}}, {"expression": {"value": "$Targeted", "expression": true}, "targetPath": {"value": "$Targeted", "expression": true}}, {"expression": {"value": "$TargetGroup", "expression": true}, "targetPath": {"value": "$TargetGroup", "expression": true}}, {"expression": {"value": "$TargetRent", "expression": true}, "targetPath": {"value": "$TargetRent", "expression": true}}, {"expression": {"value": "$TechnicalOfficer", "expression": true}, "targetPath": {"value": "$TechnicalOfficer", "expression": true}}, {"expression": {"value": "$TechnicalOfficerCode", "expression": true}, "targetPath": {"value": "$TechnicalOfficerCode", "expression": true}}, {"expression": {"value": "$UnlettableVoidReason", "expression": true}, "targetPath": {"value": "$UnlettableVoidReason", "expression": true}}, {"expression": {"value": "$Void", "expression": true}, "targetPath": {"value": "$Void", "expression": true}}, {"expression": {"value": "$YearOfConstruction", "expression": true}, "targetPath": {"value": "$YearOfConstruction", "expression": true}}]}}}, "nullSafeAccess": {"value": true}}, "view_serial": 100}, "class_build_tag": "main22460"}, "9465a11f-b417-46d4-ad5c-c9e3bb901c3f": {"class_fqid": "com-snaplogic-snaps-transform-multijoin_1-main26341", "class_id": "com-snaplogic-snaps-transform-multijoin", "class_version": 1, "instance_fqid": "9465a11f-b417-46d4-ad5c-c9e3bb901c3f_1", "instance_id": "9465a11f-b417-46d4-ad5c-c9e3bb901c3f", "instance_version": 1, "property_map": {"info": {"label": {"value": "Join  kypId"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}, "input1": {"view_type": {"value": "document"}, "label": {"value": "input1"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"availableMemoryThreshold": {"value": 20, "expression": false}, "joinPaths": {"value": [{"leftPath": {"expression": true, "value": "$ID"}, "rightPath": {"expression": true, "value": "$kypId"}, "rightInputView": {"value": "input1"}}]}, "joinType": {"value": "Left outer"}, "execution_mode": {"value": "Validate & Execute"}, "outOfResourceTimeout": {"value": 30, "expression": false}, "noMatchData": {"value": false}, "minimumMemory": {"value": 500, "expression": false}, "minimumFreeDiskSpace": {"value": 500, "expression": false}, "nullGreater": {"value": false}, "sortedStreams": {"value": "Unsorted"}, "nullSafeAccess": {"value": false}}, "view_serial": 100}, "class_build_tag": "main26341"}, "e3ae297a-17e1-4d38-a12c-2c0ca4a8cd42": {"class_fqid": "com-snaplogic-snaps-sqlserver-execute_1-main21015", "class_id": "com-snaplogic-snaps-sqlserver-execute", "class_version": 1, "instance_fqid": "e3ae297a-17e1-4d38-a12c-2c0ca4a8cd42_1", "instance_id": "e3ae297a-17e1-4d38-a12c-2c0ca4a8cd42", "instance_version": 1, "property_map": {"info": {"label": {"value": "Get PropertyStructure_Unit"}, "notes": {}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "executable_during_suggest": {"value": false}, "passThrough": {"value": true}, "maxRetryProp": {"value": 0, "expression": false}, "retryIntervalProp": {"value": 1, "expression": false}, "autoCommit": {"value": "Use account setting"}, "sqlStatement": {"value": "\"SELECT id as kypId ,Name ,AreaCode ,StreetCode ,DwellingCode ,Number ,Flat ,Street ,Town ,County ,Postcode ,isSold ,SoldDate ,isDemolished ,DemolishedDate ,Factored ,FactoredType ,CurrentStatus ,TechnicalOfficer ,PropertyType ,ConditionCode ,HousingOfficer ,IsGarageOrLockup ,Version ,CreatedBy ,CreatedOn ,LastUpdateBy ,LastUpdateOn ,LastMainRentIncrease ,NextMainRentIncrease ,PreventChargeIncreasesUntil ,PreventRentIncreasesUntil ,LastOccupancyType ,NominalCostCentre ,Void ,Aquired ,NominalDepartment ,ExcludeFromDebitRun ,TechnicalOfficerCode ,RepairsOnHold ,CommonArea ,Commercial ,AsbestosPresent ,AsbestosDetails ,isShared ,PercentageOwnedByAssocation ,CommercialUnits ,Ownership ,AreaOffice ,OwningBody ,DefaultBlock ,LocalAuthorityArea ,Demand ,UnlettableVoidReason ,YearOfConstruction ,ReasonForSale ,NonHousingUnitType ,RTBAppliedFor ,AddressLine1 ,ChargeGroup ,ChargeGroupFactored ,PropertySubType ,PropertyStyle ,SHQS ,SharedOwnershipTranche ,PropertySource ,SAPRating ,ExternalFilesFolder ,CouncilTaxBand ,TargetRent ,RtbRights ,RentRegime ,RentRegimeEffectiveDate ,ShqsStatus ,ShqsConformanceDate ,ShqsNonConformanceReason ,DefaultOccupancyType ,BuildingName ,RentSettingUnitValue ,CountyID ,RentSettingBedroomWeighting ,LocationOfMainsWaterIsolationValve ,AlternateReference ,Funder ,FinancialAnalysis1 ,FinancialAnalysis2 ,CeilingRent ,BuildYear ,BuildYearEstFrom ,BuildYearEstTo ,Pre ,Post ,BuildYearPrePost,CurrentStatus ,CurrentSubStatus ,KeysetsAvailable ,Address1Line ,Latitude ,Longitude ,LandLordReference ,LandLordName ,LandLordAddress ,LandLordPostCode ,LandLordContactTitle ,LandLordContactForename ,LandLordContactSurname ,LandLordTelephoneNumber ,LandLordFaxNumber ,LandLordMobileNumber ,LandLordEmailAddress ,LandLordStartDate ,LandLordEndDate ,LandLordTerminationDate ,LandLordExtended ,LandLordExtendedDate ,LandLordNotes ,RentOfficer ,FinanceOfficer ,Address ,NominationRights ,CouncilRates ,MarketRent ,NominalCompany ,Targeted ,TargetGroup ,CAHProgram ,MarketRentMethod ,DateRelinquished ,DisabilityModifications ,isRelinquished ,AsbestosTotalRisk ,AsbestosMaterialAssessment ,AsbestosPriorityAssessment ,AsbestosSurveyDate ,GroupHouseholdProperty ,HouseholdUnits ,CPGPreOrPostReform ,Association ,OptedOut ,CaseManagementAgency ,NRSCHHousingAgencnyIdentifier ,NRSCHManagedByOther ,NRSCHManagingAgency ,NRSCHRegisteredOwner ,NRSCHStateVestedInterest ,NRSCHStateHoldsInterest ,NRSCHAssetClass ,NRSCHMaintenanceLiability ,DecentHomeIndicator ,DecentHomeSurveyGeneratedDate ,ContiguousBoundaryIsPrimary ,ContiguousBoundaryPrimaryUnitID FROM PropertyStructure_Unit WHERE company = 1 AND ID = '\" + _unitCode + \"'\"", "expression": true}, "ignoreEmptyResult": {"value": false}}, "account": {"account_ref": {"value": {"label": {"value": "KYPERA (Live)", "label": "../../shared/KYPERA (Live)", "expression": false}, "ref_class_id": {"value": "com-snaplogic-snap-api-sql-accounts-sqlserverdatabaseaccount"}, "ref_id": {"value": "d1999d56-4b1a-4984-a906-b398bfdae8ea", "expression": false}}}}, "view_serial": 101, "input": {}, "output": {"output101": {"label": {"value": "output0"}, "view_type": {"value": "document"}}}}, "class_build_tag": "main21015"}, "af1f3ce6-8f73-4633-8800-64ad248f40b4": {"class_fqid": "com-snaplogic-snaps-transform-datatransform_4-main22460", "class_id": "com-snaplogic-snaps-transform-datatransform", "class_version": 4, "instance_fqid": "af1f3ce6-8f73-4633-8800-64ad248f40b4_1", "instance_id": "af1f3ce6-8f73-4633-8800-64ad248f40b4", "instance_version": 1, "property_map": {"info": {"label": {"value": "Map DV Property for Update"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "passThrough": {"value": false}, "transformations": {"value": {"mappingRoot": {"value": "$"}, "mappingTable": {"value": [{"expression": {"value": "1", "expression": true}, "targetPath": {"value": "$Company"}}, {"expression": {"value": "_unitCode", "expression": true}, "targetPath": {"value": "$ID"}}, {"expression": {"value": "$nch_name==null?'':$nch_name", "expression": true}, "targetPath": {"value": "$Name"}}, {"expression": {"value": "$nch_addressnumber==null?'':$nch_addressnumber", "expression": true}, "targetPath": {"value": "$Number"}}, {"expression": {"value": "$nch_addressname==null?'':$nch_addressname", "expression": true}, "targetPath": {"value": "$BuildingName"}}, {"expression": {"value": "$_nch_propertiesstreet_value==null?'0':$_nch_propertiesstreet_value", "expression": true}, "targetPath": {"value": "$StreetCode"}}, {"expression": {"value": "$nch_addressstreet", "expression": true}, "targetPath": {"value": "$Street"}}, {"expression": {"value": "$nch_addressarea", "expression": true}, "targetPath": {"value": "$AreaCode"}}, {"expression": {"value": "$nch_city==null ?'' :$nch_city", "expression": true}, "targetPath": {"value": "$Town"}}, {"expression": {"value": "$nch_postcode", "expression": true}, "targetPath": {"value": "$Postcode"}}, {"expression": {"value": "$nch_isgarage", "expression": true}, "targetPath": {"value": "$IsGarageOrLockup"}}, {"expression": {"value": "155", "expression": true}, "targetPath": {"value": "$LastUpdateBy"}}, {"expression": {"value": "$modifiedon", "expression": true}, "targetPath": {"value": "$LastUpdateOn"}}, {"expression": {"value": "null", "expression": true}, "targetPath": {"value": "$DefaultBlock"}}, {"expression": {"value": "$nch_ownershipdate", "expression": true}, "targetPath": {"value": "$Aquired"}}, {"expression": {"value": "$_nch_blocktype_value", "expression": true}, "targetPath": {"value": "$BlockTypeId"}}]}}}, "nullSafeAccess": {"value": true}}, "view_serial": 100}, "class_build_tag": "main22460"}, "8657deb4-a14b-4121-b17a-c16a6fe0f25f": {"class_fqid": "com-snaplogic-snaps-dynamics365forsales-search_1-main23721", "class_id": "com-snaplogic-snaps-dynamics365forsales-search", "class_version": 1, "instance_fqid": "8657deb4-a14b-4121-b17a-c16a6fe0f25f_1", "instance_id": "8657deb4-a14b-4121-b17a-c16a6fe0f25f", "instance_version": 1, "property_map": {"info": {"label": {"value": "Get CRM365 property"}, "notes": {}}, "input": {"input101": {"label": {"value": "input"}, "view_type": {"value": "document"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"continueOnError": {"value": false}, "retries": {"value": 0}, "select_condition_prop": {"value": []}, "execution_mode": {"value": "Validate & Execute"}, "filter_prop": {"value": [{"type_prop": {"value": "and"}, "filterAttribute_prop": {"value": "nch_unitcode", "expression": false}, "filterOperator_prop": {"value": "equal"}, "filterValue_prop": {"value": "_unitCode", "expression": true}}]}, "queryParams": {"value": []}, "pageSize": {"value": 1000, "expression": false}, "executeDuringPreview": {"value": true}, "maxPage": {"value": 0, "expression": false}, "httpHeader": {"value": []}, "objectType": {"value": "nch_property", "expression": false}, "retryDelay": {"value": 1}, "orderby_prop": {"value": []}}, "account": {"account_ref": {"value": {"label": {"value": "NCH 365 PROD", "label": "shared/NCH 365 PROD", "expression": false}, "ref_class_id": {"value": "com-snaplogic-snaps-dynamics365forsales-common-dynamics365salesoauth2account"}, "ref_id": {"value": "51ae8c12-6388-4da6-91dd-26b1cc12c1f4", "expression": false}}}}, "view_serial": 101}, "class_build_tag": "main23721"}, "46b84a2c-6dd8-4d1b-821e-d25e7df2f123": {"class_fqid": "com-snaplogic-snaps-dynamics365forsales-search_1-main23721", "class_id": "com-snaplogic-snaps-dynamics365forsales-search", "class_version": 1, "instance_fqid": "46b84a2c-6dd8-4d1b-821e-d25e7df2f123_1", "instance_id": "46b84a2c-6dd8-4d1b-821e-d25e7df2f123", "instance_version": 1, "property_map": {"info": {"label": {"value": "Get CRM365 property"}, "notes": {}}, "input": {"input101": {"label": {"value": "input"}, "view_type": {"value": "document"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"continueOnError": {"value": false}, "retries": {"value": 0}, "select_condition_prop": {"value": []}, "execution_mode": {"value": "Validate & Execute"}, "filter_prop": {"value": [{"type_prop": {"value": "and"}, "filterAttribute_prop": {"value": "nch_unitcode", "expression": false}, "filterOperator_prop": {"value": "equal"}, "filterValue_prop": {"value": "_unitCode", "expression": true}}]}, "queryParams": {"value": []}, "pageSize": {"value": 1000, "expression": false}, "executeDuringPreview": {"value": true}, "maxPage": {"value": 0, "expression": false}, "httpHeader": {"value": []}, "objectType": {"value": "nch_property", "expression": false}, "retryDelay": {"value": 1}, "orderby_prop": {"value": []}}, "account": {"account_ref": {"value": {"label": {"value": "NCH 365 PROD", "label": "shared/NCH 365 PROD", "expression": false}, "ref_class_id": {"value": "com-snaplogic-snaps-dynamics365forsales-common-dynamics365salesoauth2account"}, "ref_id": {"value": "51ae8c12-6388-4da6-91dd-26b1cc12c1f4", "expression": false}}}}, "view_serial": 101}, "class_build_tag": "main23721"}, "453cd1c0-17a6-41ec-baf2-02f5dcb8e7e4": {"class_fqid": "com-snaplogic-snaps-transform-datatransform_4-main27765", "class_id": "com-snaplogic-snaps-transform-datatransform", "class_version": 4, "instance_fqid": "453cd1c0-17a6-41ec-baf2-02f5dcb8e7e4_1", "instance_id": "453cd1c0-17a6-41ec-baf2-02f5dcb8e7e4", "instance_version": 1, "property_map": {"info": {"label": {"value": "Map CRM Property"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "passThrough": {"value": false}, "transformations": {"value": {"mappingRoot": {"value": "$"}, "mappingTable": {"value": [{"targetPath": {"value": "$nch_unitcode"}, "expression": {"value": "$nch_unitcode", "expression": true}}, {"expression": {"expression": true, "value": "$nch_accessibilityofentrance"}, "targetPath": {"value": "$nch_accessibilityofentrance"}}, {"targetPath": {"value": "$nch_addressarea"}, "expression": {"value": "$nch_addressarea", "expression": true}}, {"targetPath": {"value": "$nch_addressname"}, "expression": {"value": "$nch_addressname", "expression": true}}, {"targetPath": {"value": "$nch_addressnumber"}, "expression": {"value": "$nch_addressnumber", "expression": true}}, {"targetPath": {"value": "$nch_addressstreet"}, "expression": {"value": "$nch_addressstreet", "expression": true}}, {"targetPath": {"value": "$nch_areacustomeraccountmanager"}, "expression": {"value": "$_nch_areacustomeraccountmanager_value", "expression": true}}, {"targetPath": {"value": "$nch_areaneighbourhoodmanager"}, "expression": {"value": "$_nch_areaneighbourhoodmanager_value", "expression": true}}, {"targetPath": {"value": "$nch_assetstatus"}, "expression": {"value": "$nch_assetstatus", "expression": true}}, {"targetPath": {"value": "$nch_assettype_lu"}, "expression": {"value": "$_nch_assettype_lu_value", "expression": true}}, {"targetPath": {"value": "$nch_bikestorage"}, "expression": {"value": "$nch_bikestorage", "expression": true}}, {"targetPath": {"value": "$nch_blocktype"}, "expression": {"value": "$_nch_blocktype_value", "expression": true}}, {"targetPath": {"value": "$nch_builddate"}, "expression": {"value": "$nch_builddate", "expression": true}}, {"targetPath": {"value": "$nch_buildingtype"}, "expression": {"value": "$_nch_buildingtype_value", "expression": true}}, {"targetPath": {"value": "$nch_businesssupportadvisor"}, "expression": {"value": "$_nch_businesssupportadvisor_value", "expression": true}}, {"targetPath": {"value": "$nch_camcommercialmanager"}, "expression": {"value": "$_nch_camcommercialmanager_value", "expression": true}}, {"targetPath": {"value": "$nch_catchmentarea"}, "expression": {"value": "$_nch_catchmentarea_value", "expression": true}}, {"targetPath": {"value": "$nch_city"}, "expression": {"value": "$nch_city", "expression": true}}, {"targetPath": {"value": "$nch_cluster"}, "expression": {"value": "$nch_cluster", "expression": true}}, {"targetPath": {"value": "$nch_commercialservicesmanager"}, "expression": {"value": "$_nch_commercialservicesmanager_value", "expression": true}}, {"targetPath": {"value": "$nch_committee"}, "expression": {"value": "$_nch_committee_value", "expression": true}}, {"targetPath": {"value": "$nch_communalcaretaker"}, "expression": {"value": "$_nch_communalcaretaker_value", "expression": true}}, {"targetPath": {"value": "$nch_communalcctv"}, "expression": {"value": "$nch_communalcctv", "expression": true}}, {"targetPath": {"value": "$nch_communaldoortypekms"}, "expression": {"value": "$nch_communaldoortypekms", "expression": true}}, {"targetPath": {"value": "$nch_communalinternet"}, "expression": {"value": "$nch_communalinternet", "expression": true}}, {"targetPath": {"value": "$nch_communalsky"}, "expression": {"value": "$nch_communalsky", "expression": true}}, {"targetPath": {"value": "$nch_communaltvariel"}, "expression": {"value": "$nch_communaltvaerial", "expression": true}}, {"targetPath": {"value": "$nch_communitydevelopmentcrmsubteam"}, "expression": {"value": "$nch_communitydevelopmentcrmsubteam", "expression": true}}, {"targetPath": {"value": "$nch_contracttype"}, "expression": {"value": "$_nch_contracttype_value", "expression": true}}, {"targetPath": {"value": "$nch_counciltaxband"}, "expression": {"value": "$nch_counciltaxband", "expression": true}}, {"targetPath": {"value": "$nch_customeraccountmanager"}, "expression": {"value": "$_nch_customeraccountmanager_value", "expression": true}}, {"targetPath": {"value": "$nch_disposaldate"}, "expression": {"value": "$nch_disposaldate", "expression": true}}, {"targetPath": {"value": "$nch_dmsfiles"}, "expression": {"value": "$nch_dmsfiles", "expression": true}}, {"targetPath": {"value": "$nch_duffrynheating"}, "expression": {"value": "$nch_duffrynheating", "expression": true}}, {"targetPath": {"value": "$nch_duffrynheatingmeter"}, "expression": {"value": "$nch_duffrynheatingmeter", "expression": true}}, {"targetPath": {"value": "$nch_electricscootercharger"}, "expression": {"value": "$nch_electricscootercharger", "expression": true}}, {"targetPath": {"value": "$nch_electricvehiclecharger"}, "expression": {"value": "$nch_electricvehiclecharger", "expression": true}}, {"targetPath": {"value": "$nch_energyperformanceband"}, "expression": {"value": "$nch_energyperformanceband", "expression": true}}, {"targetPath": {"value": "$nch_entrancetype"}, "expression": {"value": "$nch_entrancetype", "expression": true}}, {"targetPath": {"value": "$nch_epcdate"}, "expression": {"value": "$nch_epcdate", "expression": true}}, {"targetPath": {"value": "$nch_estatemanager"}, "expression": {"value": "$_nch_estatemanager_value", "expression": true}}, {"targetPath": {"value": "$nch_frontgardentype"}, "expression": {"value": "$nch_frontgardentype", "expression": true}}, {"targetPath": {"value": "$nch_garagetype"}, "expression": {"value": "$nch_garagetype", "expression": true}}, {"targetPath": {"value": "$nch_gardencondition"}, "expression": {"value": "$nch_gardencondition", "expression": true}}, {"targetPath": {"value": "$nch_gardenresponsibility"}, "expression": {"value": "$nch_gardenresponsibility", "expression": true}}, {"targetPath": {"value": "$nch_gas"}, "expression": {"value": "$nch_gas", "expression": true}}, {"targetPath": {"value": "$nch_handrails"}, "expression": {"value": "$nch_handrails", "expression": true}}, {"targetPath": {"value": "$nch_hardstandingforparking"}, "expression": {"value": "$nch_hardstandingforparking", "expression": true}}, {"targetPath": {"value": "$nch_hasgarage"}, "expression": {"value": "$nch_hasgarage", "expression": true}}, {"targetPath": {"value": "$nch_housingsupportadvisor"}, "expression": {"value": "$_nch_housingsupportadvisor_value", "expression": true}}, {"targetPath": {"value": "$nch_isdemolished"}, "expression": {"value": "$nch_isdemolished", "expression": true}}, {"targetPath": {"value": "$nch_isgarage"}, "expression": {"value": "$nch_isgarage", "expression": true}}, {"targetPath": {"value": "$nch_isshared"}, "expression": {"value": "$nch_isshared", "expression": true}}, {"targetPath": {"value": "$nch_issold"}, "expression": {"value": "$nch_issold", "expression": true}}, {"targetPath": {"value": "$nch_isvoid"}, "expression": {"value": "$nch_isvoid", "expression": true}}, {"targetPath": {"value": "$nch_lastgasservice"}, "expression": {"value": "$nch_lastgasservice", "expression": true}}, {"targetPath": {"value": "$nch_lastupdatebyid"}, "expression": {"value": "$nch_lastupdatebyid", "expression": true}}, {"targetPath": {"value": "$nch_levelaccess"}, "expression": {"value": "$nch_levelaccess", "expression": true}}, {"targetPath": {"value": "$nch_localauthority"}, "expression": {"value": "$nch_localauthority", "expression": true}}, {"targetPath": {"value": "$nch_lsoa"}, "expression": {"value": "$nch_lsoa", "expression": true}}, {"targetPath": {"value": "$nch_maxoccupancy"}, "expression": {"value": "$nch_maxoccupancy", "expression": true}}, {"targetPath": {"value": "$nch_msoa"}, "expression": {"value": "$nch_msoa", "expression": true}}, {"targetPath": {"value": "$nch_name"}, "expression": {"value": "$nch_name", "expression": true}}, {"targetPath": {"value": "$nch_neighbourhoodactionteam"}, "expression": {"value": "$_nch_neighbourhoodactionteam_value", "expression": true}}, {"targetPath": {"value": "$nch_neighbourhoodmanager"}, "expression": {"value": "$_nch_neighbourhoodmanager_value", "expression": true}}, {"targetPath": {"value": "$nch_nextgasservice"}, "expression": {"value": "$nch_nextgasservice", "expression": true}}, {"targetPath": {"value": "$nch_numberofentrances"}, "expression": {"value": "$nch_numberofentrances", "expression": true}}, {"targetPath": {"value": "$nch_numberoffloorlevels"}, "expression": {"value": "$nch_numberoffloorlevels", "expression": true}}, {"targetPath": {"value": "$nch_numberofstepstoaccess"}, "expression": {"value": "$nch_numberofstepstoaccess", "expression": true}}, {"targetPath": {"value": "$nch_numberofstoreys"}, "expression": {"value": "$nch_numberofstoreys", "expression": true}}, {"targetPath": {"value": "$nch_ownershipdate"}, "expression": {"value": "$nch_ownershipdate", "expression": true}}, {"targetPath": {"value": "$nch_pimmsassetsystem"}, "expression": {"value": "$nch_pimmsassetsystem", "expression": true}}, {"targetPath": {"value": "$nch_postcode"}, "expression": {"value": "$nch_postcode", "expression": true}}, {"targetPath": {"value": "$nch_propertiesstreet"}, "expression": {"value": "$_nch_propertiesstreet_value", "expression": true}}, {"targetPath": {"value": "$nch_propertyarea"}, "expression": {"value": "$_nch_propertyarea_value", "expression": true}}, {"targetPath": {"value": "$nch_propertyclass"}, "expression": {"value": "$_nch_propertyclass_value", "expression": true}}, {"targetPath": {"value": "$nch_propertysubtype"}, "expression": {"value": "$_nch_propertysubtype_value", "expression": true}}, {"targetPath": {"value": "$nch_propertysubtypeid"}, "expression": {"value": "$nch_propertysubtypeid", "expression": true}}, {"targetPath": {"value": "$nch_propertysurveyor"}, "expression": {"value": "$_nch_propertysurveyor_value", "expression": true}}, {"targetPath": {"value": "$nch_propertytype"}, "expression": {"value": "$_nch_propertytype_value", "expression": true}}, {"targetPath": {"value": "$nch_reargardentype"}, "expression": {"value": "$nch_reargardentype", "expression": true}}, {"targetPath": {"value": "$nch_refuselocation"}, "expression": {"value": "$nch_refuselocation", "expression": true}}, {"targetPath": {"value": "$nch_relatedblock"}, "expression": {"value": "$_nch_relatedblock_value", "expression": true}}, {"targetPath": {"value": "$nch_relatedgroundsmaintenance"}, "expression": {"value": "$_nch_relatedgroundsmaintenance_value", "expression": true}}, {"targetPath": {"value": "$nch_relatedservicechargearea"}, "expression": {"value": "$_nch_relatedservicechargearea_value", "expression": true}}, {"targetPath": {"value": "$nch_relatedstreet"}, "expression": {"value": "$_nch_relatedstreet_value", "expression": true}}, {"targetPath": {"value": "$nch_relatedsubblock"}, "expression": {"value": "$_nch_relatedsubblock_value", "expression": true}}, {"targetPath": {"value": "$nch_rooftype"}, "expression": {"value": "$_nch_rooftype_value", "expression": true}}, {"targetPath": {"value": "$nch_shed"}, "expression": {"value": "$nch_shed", "expression": true}}, {"targetPath": {"value": "$nch_sidegardentype"}, "expression": {"value": "$nch_sidegardentype", "expression": true}}, {"targetPath": {"value": "$nch_sold"}, "expression": {"value": "$nch_sold", "expression": true}}, {"targetPath": {"value": "$nch_staircasedproperty"}, "expression": {"value": "$nch_staircasedproperty", "expression": true}}, {"targetPath": {"value": "$nch_tenuretype"}, "expression": {"value": "$_nch_tenuretype_value", "expression": true}}, {"targetPath": {"value": "$nch_uprnnumber"}, "expression": {"value": "$nch_uprnnumber", "expression": true}}, {"targetPath": {"value": "$nch_voidsurveyor"}, "expression": {"value": "$_nch_voidsurveyor_value", "expression": true}}, {"targetPath": {"value": "$nch_ward"}, "expression": {"value": "$_nch_ward_value", "expression": true}}, {"targetPath": {"value": "$nch_watermeterfitted"}, "expression": {"value": "$nch_watermeterfitted", "expression": true}}, {"targetPath": {"value": "$nch_watermeterfittedon"}, "expression": {"value": "$nch_watermeterfittedon", "expression": true}}, {"targetPath": {"value": "$nch_wheelchairaccessible"}, "expression": {"value": "$nch_wheelchairaccessible", "expression": true}}, {"targetPath": {"value": "$ownerid"}, "expression": {"value": "$_ownerid_value", "expression": true}}, {"targetPath": {"value": "$statecode"}, "expression": {"value": "$statecode", "expression": true}}, {"targetPath": {"value": "$statuscode"}, "expression": {"value": "$statuscode", "expression": true}}, {"targetPath": {"value": "$timezoneruleversionnumber"}, "expression": {"value": "$timezoneruleversionnumber", "expression": true}}, {"targetPath": {"value": "$utcconversiontimezonecode"}, "expression": {"value": "$utcconversiontimezonecode", "expression": true}}]}}}, "nullSafeAccess": {"value": false}}, "view_serial": 100}, "class_build_tag": "main27765"}, "4abdf61a-82db-4b5f-b9b0-69bf06c29157": {"class_fqid": "com-snaplogic-snaps-transform-datatransform_4-main22460", "class_id": "com-snaplogic-snaps-transform-datatransform", "class_version": 4, "instance_fqid": "4abdf61a-82db-4b5f-b9b0-69bf06c29157_1", "instance_id": "4abdf61a-82db-4b5f-b9b0-69bf06c29157", "instance_version": 1, "property_map": {"info": {"label": {"value": "Map Property Defaults"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "passThrough": {"value": false}, "transformations": {"value": {"mappingRoot": {"value": "$"}, "mappingTable": {"value": [{"expression": {"value": "$nch_unitcode", "expression": true}, "targetPath": {"value": "$UnitCode"}}, {"expression": {"value": "$nch_name==null?'':$nch_name", "expression": true}, "targetPath": {"value": "$Name"}}, {"expression": {"value": "$nch_addressnumber==null?'':$nch_addressnumber", "expression": true}, "targetPath": {"value": "$Number"}}, {"expression": {"value": "$nch_addressname==null?'':$nch_addressname", "expression": true}, "targetPath": {"value": "$BuildingName"}}, {"expression": {"value": "$_nch_propertiesstreet_value==null?'0':$_nch_propertiesstreet_value", "expression": true}, "targetPath": {"value": "$StreetCode"}}, {"expression": {"value": "$nch_addressstreet==null ?'' : $nch_addressstreet", "expression": true}, "targetPath": {"value": "$Street"}}, {"expression": {"value": "null", "expression": true}, "targetPath": {"value": "$AreaCode"}}, {"expression": {"value": "$nch_city==null ?'' :$nch_city", "expression": true}, "targetPath": {"value": "$Town"}}, {"expression": {"value": "$nch_postcode", "expression": true}, "targetPath": {"value": "$Postcode"}}, {"expression": {"value": "$nch_assettype_lu", "expression": true}, "targetPath": {"value": "$AssetType"}}, {"expression": {"value": "$nch_lastupdatebyid", "expression": true}, "targetPath": {"value": "$LastUpdateBy"}}, {"expression": {"value": "$modifiedon", "expression": true}, "targetPath": {"value": "$LastUpdateOn"}}, {"expression": {"value": "$_nch_relatedblock_value", "expression": true}, "targetPath": {"value": "$DefaultBlock"}}, {"expression": {"value": "$nch_blocktype", "expression": true}, "targetPath": {"value": "$BlockTypeId"}}, {"expression": {"value": "$nch_ownershipdate", "expression": true}, "targetPath": {"value": "$DateAcquired"}}, {"expression": {"value": "$nch_isgarage", "expression": true}, "targetPath": {"value": "$IsGarageOrLockup"}}]}}}, "nullSafeAccess": {"value": true}}, "view_serial": 100}, "class_build_tag": "main22460"}, "6a184be3-1171-43c6-9a79-22459736e3cf": {"class_fqid": "com-snaplogic-snaps-transform-datatransform_4-main22460", "class_id": "com-snaplogic-snaps-transform-datatransform", "class_version": 4, "instance_fqid": "6a184be3-1171-43c6-9a79-22459736e3cf_1", "instance_id": "6a184be3-1171-43c6-9a79-22459736e3cf", "instance_version": 1, "property_map": {"info": {"label": {"value": "Map Hard Coded Fields"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "passThrough": {"value": false}, "transformations": {"value": {"mappingRoot": {"value": "$"}, "mappingTable": {"value": [{"expression": {"value": "1", "expression": true}, "targetPath": {"value": "$Company"}}, {"expression": {"value": "$UnitCode", "expression": true}, "targetPath": {"value": "$ID"}}, {"expression": {"value": "$BlockTypeId ", "expression": true}, "targetPath": {"value": "$BlockTypeId "}}, {"expression": {"value": "$Name", "expression": true}, "targetPath": {"value": "$Name"}}, {"expression": {"value": "$Number", "expression": true}, "targetPath": {"value": "$Number"}}, {"expression": {"value": "$BuildingName", "expression": true}, "targetPath": {"value": "$BuildingName"}}, {"expression": {"value": "$StreetCode", "expression": true}, "targetPath": {"value": "$StreetCode"}}, {"expression": {"value": "$Street", "expression": true}, "targetPath": {"value": "$Street"}}, {"expression": {"value": "$AreaCode", "expression": true}, "targetPath": {"value": "$AreaCode"}}, {"expression": {"value": "$Town", "expression": true}, "targetPath": {"value": "$Town"}}, {"expression": {"value": "$Postcode", "expression": true}, "targetPath": {"value": "$Postcode"}}, {"expression": {"value": "''", "expression": true}, "targetPath": {"value": "$Flat"}}, {"expression": {"value": "155", "expression": true}, "targetPath": {"value": "$CreatedBy"}}, {"expression": {"value": "Date.now()", "expression": true}, "targetPath": {"value": "$CreatedOn"}}, {"expression": {"value": "155", "expression": true}, "targetPath": {"value": "$LastUpdateBy"}}, {"expression": {"value": "Date.now()", "expression": true}, "targetPath": {"value": "$LastUpdateOn"}}, {"expression": {"value": "\"1\"", "expression": true}, "targetPath": {"value": "$DwellingCode"}}, {"expression": {"value": "false", "expression": true}, "targetPath": {"value": "$RTBAppliedFor"}}, {"expression": {"value": "false", "expression": true}, "targetPath": {"value": "$Void"}}, {"expression": {"value": "1", "expression": true}, "targetPath": {"value": "$ChargeGroup"}}, {"expression": {"value": "false", "expression": true}, "targetPath": {"value": "$isShared"}}, {"expression": {"value": "false", "expression": true}, "targetPath": {"value": "$isSold"}}, {"expression": {"value": "false", "expression": true}, "targetPath": {"value": "$isDemolished"}}, {"expression": {"value": "$IsGarageOrLockup", "expression": true}, "targetPath": {"value": "$IsGarageOrLockup"}}, {"expression": {"value": "26", "expression": true}, "targetPath": {"value": "$CurrentStatus"}}, {"expression": {"value": "48", "expression": true}, "targetPath": {"value": "$CurrentSubStatus"}}, {"expression": {"value": "$DateAcquired", "expression": true}, "targetPath": {"value": "$Aquired"}}, {"expression": {"value": "$AssetType", "expression": true}, "targetPath": {"value": "$AssetType"}}]}}}, "nullSafeAccess": {"value": true}}, "view_serial": 100}, "class_build_tag": "main22460"}, "2984d891-9a01-4fce-88ce-db7d7139affd": {"class_fqid": "com-snaplogic-snaps-flow-router_2-433patches21196", "class_id": "com-snaplogic-snaps-flow-router", "class_version": 2, "instance_fqid": "2984d891-9a01-4fce-88ce-db7d7139affd_1", "instance_id": "2984d891-9a01-4fce-88ce-db7d7139affd", "instance_version": 1, "property_map": {"info": {"label": {"value": "Route BlockType"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}, "output1": {"view_type": {"value": "document"}, "label": {"value": "output1"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "routes": {"value": [{"expression": {"expression": true, "value": "$BlockType  > 2775"}, "outputViewName": {"value": "output0"}}, {"expression": {"expression": true, "value": "$BlockType == null || $BlockType <= 2775"}, "outputViewName": {"value": "output1"}}]}, "firstMatch": {"value": true}}, "view_serial": 100}, "class_build_tag": "433patches21196"}, "815bfa50-8a94-4e3c-8a63-fa2dd960d370": {"class_fqid": "com-snaplogic-snaps-flow-pipeexec_1-433patches21196", "class_id": "com-snaplogic-snaps-flow-pipeexec", "class_version": 1, "instance_fqid": "815bfa50-8a94-4e3c-8a63-fa2dd960d370_1", "instance_id": "815bfa50-8a94-4e3c-8a63-fa2dd960d370", "instance_version": 1, "property_map": {"info": {"label": {"value": "CH07 - Create Property Structure Block"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"timeout": {"value": null, "expression": false}, "pipeline": {"expression": false, "value": "CH07 - Create Kypera Property Structure Block"}, "execLabel": {"expression": false, "value": null}, "params": {"value": [{"paramValue": {"expression": true, "value": "$ID"}, "paramName": {"value": "unitCode"}}]}, "execution_mode": {"value": "Validate & Execute"}, "snaplexGovernor": {"value": "SNAPLEX_WITH_PATH"}, "retryInterval": {"value": null, "expression": false}, "retryLimit": {"value": null, "expression": false}, "reuse": {"value": false}, "snaplex": {"expression": false, "value": "NCH-SNAP-NODE"}, "executable_during_suggest": {"value": true}, "batchSize": {"value": 1, "expression": false}, "poolSize": {"value": 1, "expression": false}, "loopDocument": {"expression": true}}, "view_serial": 100}, "class_build_tag": "433patches21196"}, "9613ca88-f604-4e2c-ad92-68801d011a44": {"class_fqid": "com-snaplogic-snaps-transform-datatransform_4-main22460", "class_id": "com-snaplogic-snaps-transform-datatransform", "class_version": 4, "instance_fqid": "9613ca88-f604-4e2c-ad92-68801d011a44_1", "instance_id": "9613ca88-f604-4e2c-ad92-68801d011a44", "instance_version": 1, "property_map": {"info": {"label": {"value": "Remove BlockTypeId"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "passThrough": {"value": false}, "transformations": {"value": {"mappingRoot": {"value": "$"}, "mappingTable": {"value": [{"expression": {"value": "$Company", "expression": true}, "targetPath": {"value": "$Company"}}, {"expression": {"value": "$ID", "expression": true}, "targetPath": {"value": "$ID"}}, {"expression": {"value": "$Name", "expression": true}, "targetPath": {"value": "$Name"}}, {"expression": {"value": "$Number", "expression": true}, "targetPath": {"value": "$Number"}}, {"expression": {"value": "$BuildingName", "expression": true}, "targetPath": {"value": "$BuildingName"}}, {"expression": {"value": "$StreetCode", "expression": true}, "targetPath": {"value": "$StreetCode"}}, {"expression": {"value": "$Street", "expression": true}, "targetPath": {"value": "$Street"}}, {"expression": {"value": "$AreaCode", "expression": true}, "targetPath": {"value": "$AreaCode"}}, {"expression": {"value": "$Town", "expression": true}, "targetPath": {"value": "$Town"}}, {"expression": {"value": "$Postcode", "expression": true}, "targetPath": {"value": "$Postcode"}}, {"expression": {"value": "$Flat", "expression": true}, "targetPath": {"value": "$Flat"}}, {"expression": {"value": "$CreatedBy", "expression": true}, "targetPath": {"value": "$CreatedBy"}}, {"expression": {"value": "$CreatedOn", "expression": true}, "targetPath": {"value": "$CreatedOn"}}, {"expression": {"value": "$LastUpdateBy", "expression": true}, "targetPath": {"value": "$LastUpdateBy"}}, {"expression": {"value": "$LastUpdateOn", "expression": true}, "targetPath": {"value": "$LastUpdateOn"}}, {"expression": {"value": "$DwellingCode", "expression": true}, "targetPath": {"value": "$DwellingCode"}}, {"expression": {"value": "$RTBAppliedFor", "expression": true}, "targetPath": {"value": "$RTBAppliedFor"}}, {"expression": {"value": "$Void", "expression": true}, "targetPath": {"value": "$Void"}}, {"expression": {"value": "$ChargeGroup", "expression": true}, "targetPath": {"value": "$ChargeGroup"}}, {"expression": {"value": "$isShared", "expression": true}, "targetPath": {"value": "$isShared"}}, {"expression": {"value": "$isSold", "expression": true}, "targetPath": {"value": "$isSold"}}, {"expression": {"value": "$isDemolished", "expression": true}, "targetPath": {"value": "$isDemolished"}}, {"expression": {"value": "$IsGarageOrLockup", "expression": true}, "targetPath": {"value": "$IsGarageOrLockup"}}, {"expression": {"value": "$CurrentStatus", "expression": true}, "targetPath": {"value": "$CurrentStatus"}}, {"expression": {"value": "$CurrentSubStatus", "expression": true}, "targetPath": {"value": "$CurrentSubStatus"}}, {"expression": {"value": "$Aquired", "expression": true}, "targetPath": {"value": "$Aquired"}}, {"expression": {"value": "0", "expression": true}, "targetPath": {"value": "$Factored"}}, {"expression": {"value": "0", "expression": true}, "targetPath": {"value": "$SHQS"}}, {"expression": {"value": "0", "expression": true}, "targetPath": {"value": "$ExcludeFromDebitRun"}}]}}}, "nullSafeAccess": {"value": true}}, "view_serial": 100}, "class_build_tag": "main22460"}, "c82146e9-bb65-436a-a065-4ddd37a82507": {"class_fqid": "com-snaplogic-snaps-sqlserver-insert_1-main21015", "class_id": "com-snaplogic-snaps-sqlserver-insert", "class_version": 1, "instance_fqid": "c82146e9-bb65-436a-a065-4ddd37a82507_1", "instance_id": "c82146e9-bb65-436a-a065-4ddd37a82507", "instance_version": 1, "property_map": {"info": {"label": {"value": "Insert PropertyStructure_Unit"}, "notes": {}}, "input": {"inputRows": {"view_type": {"value": "document"}, "label": {"value": "rows"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Execute only"}, "executable_during_suggest": {"value": false}, "createTable": {"value": false}, "maxRetryProp": {"value": 0, "expression": false}, "retryIntervalProp": {"value": 1, "expression": false}, "enableIdentityInsert": {"value": false}, "schemaName": {"value": "dbo", "expression": false}, "tableName": {"value": "PropertyStructure_Unit", "expression": false}}, "account": {"account_ref": {"value": {"label": {"value": "KYPERA (Live)", "label": "../shared/KYPERA (Live)", "expression": false}, "ref_class_id": {"value": "com-snaplogic-snap-api-sql-accounts-sqlserverdatabaseaccount"}, "ref_id": {"value": "d1999d56-4b1a-4984-a906-b398bfdae8ea", "expression": false}}}}, "view_serial": 102, "output": {"output102": {"label": {"value": "output"}, "view_type": {"value": "document"}}}}, "class_build_tag": "main21015"}, "d79bc16b-5ead-4610-8006-1c81126748af": {"class_fqid": "com-snaplogic-snaps-flow-pipeexec_1-433patches21196", "class_id": "com-snaplogic-snaps-flow-pipeexec", "class_version": 1, "instance_fqid": "d79bc16b-5ead-4610-8006-1c81126748af_1", "instance_id": "d79bc16b-5ead-4610-8006-1c81126748af", "instance_version": 1, "property_map": {"info": {"label": {"value": "CH06 - <PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"timeout": {"value": null, "expression": false}, "pipeline": {"expression": false, "value": "CH06 - <PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "execLabel": {"expression": false, "value": null}, "params": {"value": [{"paramValue": {"expression": true, "value": "$UnitCode"}, "paramName": {"value": "UnitCode"}}]}, "execution_mode": {"value": "Validate & Execute"}, "snaplexGovernor": {"value": "SNAPLEX_WITH_PATH"}, "retryInterval": {"value": null, "expression": false}, "retryLimit": {"value": null, "expression": false}, "reuse": {"value": false}, "snaplex": {"expression": false, "value": "NCH-SNAP-NODE"}, "executable_during_suggest": {"value": true}, "batchSize": {"value": 1, "expression": false}, "poolSize": {"value": 1, "expression": false}, "loopDocument": {"expression": true}}, "view_serial": 100}, "class_build_tag": "433patches21196"}, "08a58faa-582c-4fbf-a392-ce92594ac22a": {"class_fqid": "com-snaplogic-snaps-flow-pipeexec_1-433patches21196", "class_id": "com-snaplogic-snaps-flow-pipeexec", "class_version": 1, "instance_fqid": "08a58faa-582c-4fbf-a392-ce92594ac22a_1", "instance_id": "08a58faa-582c-4fbf-a392-ce92594ac22a", "instance_version": 1, "property_map": {"info": {"label": {"value": "CH12 - C<PERSON> <PERSON><PERSON><PERSON> Waiting List List Unit"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"timeout": {"value": null, "expression": false}, "pipeline": {"expression": false, "value": "CH12 - C<PERSON> <PERSON><PERSON><PERSON> Waiting List List Unit"}, "execLabel": {"expression": false, "value": null}, "params": {"value": [{"paramValue": {"expression": true, "value": "$UnitCode"}, "paramName": {"value": "unitCode"}}]}, "maxRetryProp": {"value": 0, "expression": false}, "execution_mode": {"value": "Validate & Execute"}, "snaplexGovernor": {"value": "SNAPLEX_WITH_PATH"}, "schemaName": {"value": null, "expression": false}, "retryInterval": {"value": null, "expression": false}, "retryIntervalProp": {"value": 1, "expression": false}, "createTable": {"value": false}, "retryLimit": {"value": null, "expression": false}, "reuse": {"value": false}, "snaplex": {"expression": false, "value": "NCH-SNAP-NODE"}, "tableName": {"value": null, "expression": false}, "enableIdentityInsert": {"value": false}, "executable_during_suggest": {"value": true}, "batchSize": {"value": 1, "expression": false}, "poolSize": {"value": 1, "expression": false}, "loopDocument": {"expression": true}}, "view_serial": 100}, "class_build_tag": "433patches21196"}, "af33e159-dc0a-45b3-96d0-c0b032b812e2": {"class_fqid": "com-snaplogic-snaps-flow-copy_1-main27765", "class_id": "com-snaplogic-snaps-flow-copy", "class_version": 1, "instance_fqid": "af33e159-dc0a-45b3-96d0-c0b032b812e2_1", "instance_id": "af33e159-dc0a-45b3-96d0-c0b032b812e2", "instance_version": 1, "property_map": {"info": {"label": {"value": "Copy"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}, "output1": {"view_type": {"value": "document"}, "label": {"value": "output1"}}, "output102": {"label": {"value": "output2"}, "view_type": {"value": "document"}}, "output104": {"label": {"value": "output3"}, "view_type": {"value": "document"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}}, "view_serial": 104}, "class_build_tag": "main27765"}, "52cfe18a-d022-4ca5-8bc4-ee08cfac6a31": {"class_fqid": "com-snaplogic-snaps-flow-pipeexec_1-433patches21196", "class_id": "com-snaplogic-snaps-flow-pipeexec", "class_version": 1, "instance_fqid": "52cfe18a-d022-4ca5-8bc4-ee08cfac6a31_1", "instance_id": "52cfe18a-d022-4ca5-8bc4-ee08cfac6a31", "instance_version": 1, "property_map": {"info": {"label": {"value": "CH04 - Refresh Occupants"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"timeout": {"value": null, "expression": false}, "pipeline": {"expression": false, "value": "CH04 - <PERSON><PERSON><PERSON>ccup<PERSON>"}, "execLabel": {"expression": true, "value": null}, "params": {"value": [{"paramValue": {"expression": true, "value": "_unitCode"}, "paramName": {"value": "unitCode"}}, {"paramName": {"value": "updateCrm"}, "paramValue": {"value": "true", "expression": true}}]}, "execution_mode": {"value": "Validate & Execute"}, "snaplexGovernor": {"value": "SNAPLEX_WITH_PATH"}, "retryInterval": {"value": null, "expression": false}, "retryLimit": {"value": null, "expression": false}, "reuse": {"value": false}, "snaplex": {"expression": false}, "executable_during_suggest": {"value": true}, "batchSize": {"value": 1, "expression": false}, "poolSize": {"value": 1, "expression": false}, "loopDocument": {"expression": true}}, "view_serial": 100}, "class_build_tag": "433patches21196"}, "304b5ccc-acea-4732-9656-5018e7140aca": {"class_fqid": "com-snaplogic-snaps-flow-pipeexec_1-433patches21196", "class_id": "com-snaplogic-snaps-flow-pipeexec", "class_version": 1, "instance_fqid": "304b5ccc-acea-4732-9656-5018e7140aca_1", "instance_id": "304b5ccc-acea-4732-9656-5018e7140aca", "instance_version": 1, "property_map": {"info": {"label": {"value": "CH03 - Update Kypera Property Characteristics"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "ignore"}}, "settings": {"timeout": {"value": null, "expression": false}, "pipeline": {"expression": false, "value": "CH03 - Update Kypera Property Characteristics"}, "execLabel": {"expression": false, "value": null}, "params": {"value": [{"paramValue": {"expression": true, "value": "_unitCode"}, "paramName": {"value": "unitCode"}}]}, "execution_mode": {"value": "Validate & Execute"}, "snaplexGovernor": {"value": "SNAPLEX_WITH_PATH"}, "retryInterval": {"value": null, "expression": false}, "retryLimit": {"value": null, "expression": false}, "reuse": {"value": false}, "snaplex": {"expression": false, "value": "NCH-SNAP-NODE"}, "executable_during_suggest": {"value": true}, "batchSize": {"value": 1, "expression": false}, "poolSize": {"value": 1, "expression": false}, "loopDocument": {"expression": true}}, "view_serial": 100}, "class_build_tag": "433patches21196"}, "93894540-7ecb-4a47-bca1-19aaa2b29f58": {"class_fqid": "com-snaplogic-snaps-flow-pipeexec_1-433patches21196", "class_id": "com-snaplogic-snaps-flow-pipeexec", "class_version": 1, "instance_fqid": "93894540-7ecb-4a47-bca1-19aaa2b29f58_1", "instance_id": "93894540-7ecb-4a47-bca1-19aaa2b29f58", "instance_version": 1, "property_map": {"info": {"label": {"value": "P02 -  Supplementary Updates"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"timeout": {"value": null, "expression": false}, "pipeline": {"expression": false, "value": "P02 - Property CRM365 to Kypera - Supplementary Updates"}, "execLabel": {"expression": false, "value": null}, "params": {"value": [{"paramValue": {"expression": true, "value": "$UnitCode"}, "paramName": {"value": "unitCode"}}]}, "execution_mode": {"value": "Validate & Execute"}, "snaplexGovernor": {"value": "SNAPLEX_WITH_PATH"}, "retryInterval": {"value": null, "expression": false}, "retryLimit": {"value": null, "expression": false}, "reuse": {"value": false}, "snaplex": {"expression": false, "value": "NCH-SNAP-NODE"}, "executable_during_suggest": {"value": true}, "batchSize": {"value": 1, "expression": false}, "poolSize": {"value": 1, "expression": false}, "loopDocument": {"expression": true}}, "view_serial": 100}, "class_build_tag": "433patches21196"}, "ad1243d4-5b85-4dc2-80d4-bd23d93b5298": {"class_fqid": "com-snaplogic-snaps-transform-datatransform_4-main22460", "class_id": "com-snaplogic-snaps-transform-datatransform", "class_version": 4, "instance_fqid": "ad1243d4-5b85-4dc2-80d4-bd23d93b5298_1", "instance_id": "ad1243d4-5b85-4dc2-80d4-bd23d93b5298", "instance_version": 1, "property_map": {"info": {"label": {"value": "Map Property ID"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "passThrough": {"value": false}, "transformations": {"value": {"mappingRoot": {"value": "$"}, "mappingTable": {"value": [{"expression": {"value": "$original.ID", "expression": true}, "targetPath": {"value": "$UnitCode"}}]}}}, "nullSafeAccess": {"value": false}}, "view_serial": 100}, "class_build_tag": "main22460"}, "4e328d99-dd9b-4bae-a8b8-a78de23a211d": {"class_fqid": "com-snaplogic-snaps-flow-pipeexec_1-433patches21196", "class_id": "com-snaplogic-snaps-flow-pipeexec", "class_version": 1, "instance_fqid": "4e328d99-dd9b-4bae-a8b8-a78de23a211d_1", "instance_id": "4e328d99-dd9b-4bae-a8b8-a78de23a211d", "instance_version": 1, "property_map": {"info": {"label": {"value": "P02 -  Supplementary Updates"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"timeout": {"value": null, "expression": false}, "pipeline": {"expression": false, "value": "P02 - Property CRM365 to Kypera - Supplementary Updates"}, "execLabel": {"expression": false, "value": null}, "params": {"value": [{"paramValue": {"expression": true, "value": "$original.UnitCode"}, "paramName": {"value": "unitCode"}}]}, "execution_mode": {"value": "Validate & Execute"}, "snaplexGovernor": {"value": "SNAPLEX_WITH_PATH"}, "retryInterval": {"value": null, "expression": false}, "retryLimit": {"value": null, "expression": false}, "reuse": {"value": false}, "snaplex": {"expression": false, "value": "NCH-SNAP-NODE"}, "executable_during_suggest": {"value": true}, "batchSize": {"value": 1, "expression": false}, "poolSize": {"value": 1, "expression": false}, "loopDocument": {"expression": true}}, "view_serial": 100}, "class_build_tag": "433patches21196"}, "2c42b9de-a028-45e0-a07c-77e13e6bd3bf": {"class_fqid": "com-snaplogic-snaps-transform-datatransform_4-main22460", "class_id": "com-snaplogic-snaps-transform-datatransform", "class_version": 4, "instance_fqid": "2c42b9de-a028-45e0-a07c-77e13e6bd3bf_1", "instance_id": "2c42b9de-a028-45e0-a07c-77e13e6bd3bf", "instance_version": 1, "property_map": {"info": {"label": {"value": "Map DV Property"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "passThrough": {"value": false}, "transformations": {"value": {"mappingRoot": {"value": "$"}, "mappingTable": {"value": [{"expression": {"value": "$original.ID", "expression": true}, "targetPath": {"value": "$UnitCode"}}, {"expression": {"value": "1", "expression": true}, "targetPath": {"value": "$Company"}}, {"expression": {"value": "$original.LastUpdateBy", "expression": true}, "targetPath": {"value": "$LastUpdateBy"}}, {"expression": {"value": "true", "expression": true}, "targetPath": {"value": "$updateCrm"}}]}}}, "nullSafeAccess": {"value": false}}, "view_serial": 100}, "class_build_tag": "main22460"}, "8ee6f782-9415-40d3-b026-1fb0d58ac028": {"class_fqid": "com-snaplogic-snaps-flow-copy_1-main27765", "class_id": "com-snaplogic-snaps-flow-copy", "class_version": 1, "instance_fqid": "8ee6f782-9415-40d3-b026-1fb0d58ac028_1", "instance_id": "8ee6f782-9415-40d3-b026-1fb0d58ac028", "instance_version": 1, "property_map": {"info": {"label": {"value": "Copy"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}, "output1": {"view_type": {"value": "document"}, "label": {"value": "output1"}}, "output102": {"label": {"value": "output2"}, "view_type": {"value": "document"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}}, "view_serial": 103}, "class_build_tag": "main27765"}, "d45382f7-a63d-42e0-bcdc-f148b19fcbe1": {"class_fqid": "com-snaplogic-snaps-flow-pipeexec_1-433patches21196", "class_id": "com-snaplogic-snaps-flow-pipeexec", "class_version": 1, "instance_fqid": "d45382f7-a63d-42e0-bcdc-f148b19fcbe1_1", "instance_id": "d45382f7-a63d-42e0-bcdc-f148b19fcbe1", "instance_version": 1, "property_map": {"info": {"label": {"value": "CH07 - Create Property Structure Block"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output101": {"label": {"value": "output0"}, "view_type": {"value": "document"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"timeout": {"value": null, "expression": false}, "pipeline": {"expression": false, "value": "CH07 - Create Kypera Property Structure Block"}, "execLabel": {"expression": false, "value": null}, "params": {"value": [{"paramValue": {"expression": true, "value": "$UnitCode"}, "paramName": {"value": "unitCode"}}]}, "execution_mode": {"value": "Validate & Execute"}, "snaplexGovernor": {"value": "SNAPLEX_WITH_PATH"}, "retryInterval": {"value": null, "expression": false}, "retryLimit": {"value": null, "expression": false}, "reuse": {"value": false}, "snaplex": {"expression": false, "value": "NCH-SNAP-NODE"}, "executable_during_suggest": {"value": true}, "batchSize": {"value": 1, "expression": false}, "poolSize": {"value": 1, "expression": false}, "loopDocument": {"expression": true}}, "view_serial": 101}, "class_build_tag": "433patches21196"}, "b4942172-e2fd-4b83-b219-e6c7a8133634": {"class_fqid": "com-snaplogic-snaps-dynamics365forsales-search_1-main23721", "class_id": "com-snaplogic-snaps-dynamics365forsales-search", "class_version": 1, "instance_fqid": "b4942172-e2fd-4b83-b219-e6c7a8133634_1", "instance_id": "b4942172-e2fd-4b83-b219-e6c7a8133634", "instance_version": 1, "property_map": {"info": {"label": {"value": "Get block type"}, "notes": {}}, "input": {"input101": {"label": {"value": "input"}, "view_type": {"value": "document"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"continueOnError": {"value": true}, "retries": {"value": 0}, "select_condition_prop": {"value": [{"select_prop": {"value": "nch_optionsetidkyperavalue", "expression": false}}]}, "execution_mode": {"value": "Validate & Execute"}, "filter_prop": {"value": [{"type_prop": {"value": "and"}, "filterAttribute_prop": {"value": "nch_propertyblocktypeid", "expression": false}, "filterOperator_prop": {"value": "equal"}, "filterValue_prop": {"value": "$BlockTypeId", "expression": true}}]}, "queryParams": {"value": []}, "pageSize": {"value": 1000, "expression": false}, "executeDuringPreview": {"value": true}, "maxPage": {"value": 0, "expression": false}, "httpHeader": {"value": []}, "objectType": {"value": "nch_propertyblocktype", "expression": false}, "retryDelay": {"value": 1}, "orderby_prop": {"value": []}}, "account": {"account_ref": {"value": {"label": {"value": "NCH 365 PROD", "label": "shared/NCH 365 PROD", "expression": false}, "ref_class_id": {"value": "com-snaplogic-snaps-dynamics365forsales-common-dynamics365salesoauth2account"}, "ref_id": {"value": "51ae8c12-6388-4da6-91dd-26b1cc12c1f4", "expression": false}}}}, "view_serial": 101}, "class_build_tag": "main23721"}, "28dddefe-6abd-4081-a45e-8f65e188b44b": {"class_fqid": "com-snaplogic-snaps-transform-datatransform_4-main22460", "class_id": "com-snaplogic-snaps-transform-datatransform", "class_version": 4, "instance_fqid": "28dddefe-6abd-4081-a45e-8f65e188b44b_1", "instance_id": "28dddefe-6abd-4081-a45e-8f65e188b44b", "instance_version": 1, "property_map": {"info": {"label": {"value": "Map BlockTypeId"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "passThrough": {"value": false}, "transformations": {"value": {"mappingRoot": {"value": "$"}, "mappingTable": {"value": [{"expression": {"value": "$original.Company", "expression": true}, "targetPath": {"value": "$Company"}}, {"expression": {"value": "$original.ID", "expression": true}, "targetPath": {"value": "$ID"}}, {"expression": {"value": "$original.Name", "expression": true}, "targetPath": {"value": "$Name"}}, {"expression": {"value": "$original.Number", "expression": true}, "targetPath": {"value": "$Number"}}, {"expression": {"value": "$original.BuildingName", "expression": true}, "targetPath": {"value": "$BuildingName"}}, {"expression": {"value": "$original.StreetCode", "expression": true}, "targetPath": {"value": "$StreetCode"}}, {"expression": {"value": "$original.Street", "expression": true}, "targetPath": {"value": "$Street"}}, {"expression": {"value": "$original.AreaCode", "expression": true}, "targetPath": {"value": "$AreaCode"}}, {"expression": {"value": "$original.Town", "expression": true}, "targetPath": {"value": "$Town"}}, {"expression": {"value": "$original.Postcode", "expression": true}, "targetPath": {"value": "$Postcode"}}, {"expression": {"value": "$original.Flat", "expression": true}, "targetPath": {"value": "$Flat"}}, {"expression": {"value": "$original.CreatedBy", "expression": true}, "targetPath": {"value": "$CreatedBy"}}, {"expression": {"value": "$original.CreatedOn", "expression": true}, "targetPath": {"value": "$CreatedOn"}}, {"expression": {"value": "$original.LastUpdateBy", "expression": true}, "targetPath": {"value": "$LastUpdateBy"}}, {"expression": {"value": "$original.LastUpdateOn", "expression": true}, "targetPath": {"value": "$LastUpdateOn"}}, {"expression": {"value": "$original.DwellingCode", "expression": true}, "targetPath": {"value": "$DwellingCode"}}, {"expression": {"value": "$original.RTBAppliedFor", "expression": true}, "targetPath": {"value": "$RTBAppliedFor"}}, {"expression": {"value": "$original.Void", "expression": true}, "targetPath": {"value": "$Void"}}, {"expression": {"value": "$original.ChargeGroup", "expression": true}, "targetPath": {"value": "$ChargeGroup"}}, {"expression": {"value": "$original.isShared", "expression": true}, "targetPath": {"value": "$isShared"}}, {"expression": {"value": "$original.isSold", "expression": true}, "targetPath": {"value": "$isSold"}}, {"expression": {"value": "$original.isDemolished", "expression": true}, "targetPath": {"value": "$isDemolished"}}, {"expression": {"value": "$original.IsGarageOrLockup", "expression": true}, "targetPath": {"value": "$IsGarageOrLockup"}}, {"expression": {"value": "$original.CurrentStatus", "expression": true}, "targetPath": {"value": "$CurrentStatus"}}, {"expression": {"value": "$original.CurrentSubStatus", "expression": true}, "targetPath": {"value": "$CurrentSubStatus"}}, {"expression": {"value": "$original.Aquired", "expression": true}, "targetPath": {"value": "$Aquired"}}, {"expression": {"value": "0", "expression": true}, "targetPath": {"value": "$Factored"}}, {"expression": {"value": "0", "expression": true}, "targetPath": {"value": "$SHQS"}}, {"expression": {"value": "0", "expression": true}, "targetPath": {"value": "$ExcludeFromDebitRun"}}, {"expression": {"value": "$nch_optionsetidkyperavalue", "expression": true}, "targetPath": {"value": "$BlockType"}}]}}}, "nullSafeAccess": {"value": true}}, "view_serial": 100}, "class_build_tag": "main22460"}, "b0f71648-c1fc-46d6-b64f-dcc44d44c633": {"class_fqid": "com-snaplogic-snaps-flow-union_2-433patches21196", "class_id": "com-snaplogic-snaps-flow-union", "class_version": 2, "instance_fqid": "b0f71648-c1fc-46d6-b64f-dcc44d44c633_1", "instance_id": "b0f71648-c1fc-46d6-b64f-dcc44d44c633", "instance_version": 1, "property_map": {"info": {"label": {"value": "Union"}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}, "input1": {"view_type": {"value": "document"}, "label": {"value": "input1"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}}, "view_serial": 100}, "class_build_tag": "433patches21196"}, "be52fd8a-f0fc-4c8f-8259-001ecedbb75c": {"class_fqid": "com-snaplogic-snaps-flow-router_2-433patches21196", "class_id": "com-snaplogic-snaps-flow-router", "class_version": 2, "instance_fqid": "be52fd8a-f0fc-4c8f-8259-001ecedbb75c_1", "instance_id": "be52fd8a-f0fc-4c8f-8259-001ecedbb75c", "instance_version": 1, "property_map": {"info": {"label": {"value": "Route BlockTypeId"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}, "output1": {"view_type": {"value": "document"}, "label": {"value": "output1"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "routes": {"value": [{"expression": {"expression": true, "value": "$BlockTypeId != null"}, "outputViewName": {"value": "output0"}}, {"expression": {"expression": true, "value": "$BlockTypeId == null"}, "outputViewName": {"value": "output1"}}]}, "firstMatch": {"value": false}}, "view_serial": 100}, "class_build_tag": "433patches21196"}, "70a3bf8a-ce84-4860-8254-bb4ccb3dd514": {"class_fqid": "com-snaplogic-snaps-flow-router_2-433patches21196", "class_id": "com-snaplogic-snaps-flow-router", "class_version": 2, "instance_fqid": "70a3bf8a-ce84-4860-8254-bb4ccb3dd514_1", "instance_id": "70a3bf8a-ce84-4860-8254-bb4ccb3dd514", "instance_version": 1, "property_map": {"info": {"label": {"value": "Route BlockTypeId"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}, "output1": {"view_type": {"value": "document"}, "label": {"value": "output1"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "routes": {"value": [{"expression": {"expression": true, "value": "$BlockTypeId != null"}, "outputViewName": {"value": "output0"}}, {"expression": {"expression": true, "value": "$BlockTypeId == null"}, "outputViewName": {"value": "output1"}}]}, "firstMatch": {"value": false}}, "view_serial": 100}, "class_build_tag": "433patches21196"}, "f7d66690-a04d-4401-86bf-24c1100dca76": {"class_fqid": "com-snaplogic-snaps-dynamics365forsales-search_1-main23721", "class_id": "com-snaplogic-snaps-dynamics365forsales-search", "class_version": 1, "instance_fqid": "f7d66690-a04d-4401-86bf-24c1100dca76_1", "instance_id": "f7d66690-a04d-4401-86bf-24c1100dca76", "instance_version": 1, "property_map": {"info": {"label": {"value": "Get block type"}, "notes": {}}, "input": {"input101": {"label": {"value": "input"}, "view_type": {"value": "document"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"continueOnError": {"value": true}, "retries": {"value": 0}, "select_condition_prop": {"value": [{"select_prop": {"value": "nch_optionsetidkyperavalue", "expression": false}}]}, "execution_mode": {"value": "Validate & Execute"}, "filter_prop": {"value": [{"type_prop": {"value": "and"}, "filterAttribute_prop": {"value": "nch_propertyblocktypeid", "expression": false}, "filterOperator_prop": {"value": "equal"}, "filterValue_prop": {"value": "$BlockTypeId", "expression": true}}]}, "queryParams": {"value": []}, "pageSize": {"value": 1000, "expression": false}, "executeDuringPreview": {"value": true}, "maxPage": {"value": 0, "expression": false}, "httpHeader": {"value": []}, "objectType": {"value": "nch_propertyblocktype", "expression": false}, "retryDelay": {"value": 1}, "orderby_prop": {"value": []}}, "account": {"account_ref": {"value": {"label": {"value": "NCH 365 PROD", "label": "shared/NCH 365 PROD", "expression": false}, "ref_class_id": {"value": "com-snaplogic-snaps-dynamics365forsales-common-dynamics365salesoauth2account"}, "ref_id": {"value": "51ae8c12-6388-4da6-91dd-26b1cc12c1f4", "expression": false}}}}, "view_serial": 101}, "class_build_tag": "main23721"}, "d3b08f27-c4b9-4956-b332-c9adc87e9c9d": {"class_fqid": "com-snaplogic-snaps-transform-datatransform_4-main22460", "class_id": "com-snaplogic-snaps-transform-datatransform", "class_version": 4, "instance_fqid": "d3b08f27-c4b9-4956-b332-c9adc87e9c9d_1", "instance_id": "d3b08f27-c4b9-4956-b332-c9adc87e9c9d", "instance_version": 1, "property_map": {"info": {"label": {"value": "Map BlockTypeId"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "passThrough": {"value": false}, "transformations": {"value": {"mappingRoot": {"value": "$"}, "mappingTable": {"value": [{"expression": {"value": "$original.Company", "expression": true}, "targetPath": {"value": "$Company"}}, {"expression": {"value": "$original.ID", "expression": true}, "targetPath": {"value": "$ID"}}, {"expression": {"value": "$original.Name", "expression": true}, "targetPath": {"value": "$Name"}}, {"expression": {"value": "$original.Number", "expression": true}, "targetPath": {"value": "$Number"}}, {"expression": {"value": "$original.BuildingName", "expression": true}, "targetPath": {"value": "$BuildingName"}}, {"expression": {"value": "$original.StreetCode", "expression": true}, "targetPath": {"value": "$StreetCode"}}, {"expression": {"value": "$original.Street", "expression": true}, "targetPath": {"value": "$Street"}}, {"expression": {"value": "$original.AreaCode", "expression": true}, "targetPath": {"value": "$AreaCode"}}, {"expression": {"value": "$original.Town", "expression": true}, "targetPath": {"value": "$Town"}}, {"expression": {"value": "$original.Postcode", "expression": true}, "targetPath": {"value": "$Postcode"}}, {"expression": {"value": "$original.PropertyType", "expression": true}, "targetPath": {"value": "$PropertyType"}}, {"expression": {"value": "$original.PropertySubType", "expression": true}, "targetPath": {"value": "$PropertySubType"}}, {"expression": {"value": "$original.Flat", "expression": true}, "targetPath": {"value": "$Flat"}}, {"expression": {"value": "$original.CreatedBy", "expression": true}, "targetPath": {"value": "$CreatedBy"}}, {"expression": {"value": "$original.CreatedOn", "expression": true}, "targetPath": {"value": "$CreatedOn"}}, {"expression": {"value": "$original.LastUpdateBy", "expression": true}, "targetPath": {"value": "$LastUpdateBy"}}, {"expression": {"value": "$original.LastUpdateOn", "expression": true}, "targetPath": {"value": "$LastUpdateOn"}}, {"expression": {"value": "$original.DwellingCode", "expression": true}, "targetPath": {"value": "$DwellingCode"}}, {"expression": {"value": "$original.RTBAppliedFor", "expression": true}, "targetPath": {"value": "$RTBAppliedFor"}}, {"expression": {"value": "$original.Void", "expression": true}, "targetPath": {"value": "$Void"}}, {"expression": {"value": "$original.isShared", "expression": true}, "targetPath": {"value": "$isShared"}}, {"expression": {"value": "$original.isSold", "expression": true}, "targetPath": {"value": "$isSold"}}, {"expression": {"value": "$original.isDemolished", "expression": true}, "targetPath": {"value": "$isDemolished"}}, {"expression": {"value": "$original.IsGarageOrLockup", "expression": true}, "targetPath": {"value": "$IsGarageOrLockup"}}, {"expression": {"value": "$original.Aquired", "expression": true}, "targetPath": {"value": "$Aquired"}}, {"expression": {"value": "0", "expression": true}, "targetPath": {"value": "$SHQS"}}, {"expression": {"value": "0", "expression": true}, "targetPath": {"value": "$ExcludeFromDebitRun"}}, {"expression": {"value": "$nch_optionsetidkyperavalue", "expression": true}, "targetPath": {"value": "$BlockType"}}]}}}, "nullSafeAccess": {"value": true}}, "view_serial": 100}, "class_build_tag": "main22460"}, "da9fdd93-0d1a-4442-89c4-ec920a0ca4f7": {"class_fqid": "com-snaplogic-snaps-flow-router_2-433patches21196", "class_id": "com-snaplogic-snaps-flow-router", "class_version": 2, "instance_fqid": "da9fdd93-0d1a-4442-89c4-ec920a0ca4f7_1", "instance_id": "da9fdd93-0d1a-4442-89c4-ec920a0ca4f7", "instance_version": 1, "property_map": {"info": {"label": {"value": "Route BlockType"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}, "output1": {"view_type": {"value": "document"}, "label": {"value": "output1"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "routes": {"value": [{"expression": {"expression": true, "value": "$BlockType  > 2775"}, "outputViewName": {"value": "output0"}}, {"expression": {"expression": true, "value": "$BlockType == null || $BlockType <= 2775"}, "outputViewName": {"value": "output1"}}]}, "firstMatch": {"value": true}}, "view_serial": 100}, "class_build_tag": "433patches21196"}, "7ad7f835-6cf7-4e10-afc0-82e418868169": {"class_fqid": "com-snaplogic-snaps-flow-union_2-433patches21196", "class_id": "com-snaplogic-snaps-flow-union", "class_version": 2, "instance_fqid": "7ad7f835-6cf7-4e10-afc0-82e418868169_1", "instance_id": "7ad7f835-6cf7-4e10-afc0-82e418868169", "instance_version": 1, "property_map": {"info": {"label": {"value": "Union"}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}, "input1": {"view_type": {"value": "document"}, "label": {"value": "input1"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}}, "view_serial": 100}, "class_build_tag": "433patches21196"}, "dae1a6ec-fb55-457e-a321-10dd84c0fe2c": {"class_fqid": "com-snaplogic-snaps-flow-pipeexec_1-433patches21196", "class_id": "com-snaplogic-snaps-flow-pipeexec", "class_version": 1, "instance_fqid": "dae1a6ec-fb55-457e-a321-10dd84c0fe2c_1", "instance_id": "dae1a6ec-fb55-457e-a321-10dd84c0fe2c", "instance_version": 1, "property_map": {"info": {"label": {"value": "CH07B - Update Property Structure Block"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"timeout": {"value": null, "expression": false}, "pipeline": {"expression": false, "value": "CH07B - Update Kypera Property Structure Block"}, "execLabel": {"expression": false, "value": null}, "params": {"value": [{"paramValue": {"expression": true, "value": "$UnitCode"}, "paramName": {"value": "unitCode"}}]}, "execution_mode": {"value": "Validate & Execute"}, "snaplexGovernor": {"value": "SNAPLEX_WITH_PATH"}, "retryInterval": {"value": null, "expression": false}, "retryLimit": {"value": null, "expression": false}, "reuse": {"value": false}, "snaplex": {"expression": false, "value": "NCH-SNAP-NODE"}, "executable_during_suggest": {"value": true}, "batchSize": {"value": 1, "expression": false}, "poolSize": {"value": 1, "expression": false}, "loopDocument": {"expression": true}}, "view_serial": 101}, "class_build_tag": "433patches21196"}, "bec92873-dab7-4248-812b-38f1204c09cd": {"class_fqid": "com-snaplogic-snaps-transform-datatransform_4-main22460", "class_id": "com-snaplogic-snaps-transform-datatransform", "class_version": 4, "instance_fqid": "bec92873-dab7-4248-812b-38f1204c09cd_1", "instance_id": "bec92873-dab7-4248-812b-38f1204c09cd", "instance_version": 1, "property_map": {"info": {"label": {"value": "Map BlockTypeId"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "passThrough": {"value": false}, "transformations": {"value": {"mappingRoot": {"value": "$"}, "mappingTable": {"value": [{"expression": {"value": "$Company", "expression": true}, "targetPath": {"value": "$Company"}}, {"expression": {"value": "$ID", "expression": true}, "targetPath": {"value": "$ID"}}, {"expression": {"value": "$Name", "expression": true}, "targetPath": {"value": "$Name"}}, {"expression": {"value": "$Number", "expression": true}, "targetPath": {"value": "$Number"}}, {"expression": {"value": "$BuildingName", "expression": true}, "targetPath": {"value": "$BuildingName"}}, {"expression": {"value": "$AreaCode", "expression": true}, "targetPath": {"value": "$AreaCode"}}, {"expression": {"value": "$Town", "expression": true}, "targetPath": {"value": "$Town"}}, {"expression": {"value": "$Postcode", "expression": true}, "targetPath": {"value": "$Postcode"}}, {"expression": {"value": "$PropertyType", "expression": true}, "targetPath": {"value": "$PropertyType"}}, {"expression": {"value": "$PropertySubType", "expression": true}, "targetPath": {"value": "$PropertySubType"}}, {"expression": {"value": "$Flat", "expression": true}, "targetPath": {"value": "$Flat"}}, {"expression": {"value": "$CreatedBy", "expression": true}, "targetPath": {"value": "$CreatedBy"}}, {"expression": {"value": "$CreatedOn", "expression": true}, "targetPath": {"value": "$CreatedOn"}}, {"expression": {"value": "$LastUpdateBy", "expression": true}, "targetPath": {"value": "$LastUpdateBy"}}, {"expression": {"value": "$LastUpdateOn", "expression": true}, "targetPath": {"value": "$LastUpdateOn"}}, {"expression": {"value": "$DwellingCode", "expression": true}, "targetPath": {"value": "$DwellingCode"}}, {"expression": {"value": "$RTBAppliedFor", "expression": true}, "targetPath": {"value": "$RTBAppliedFor"}}, {"expression": {"value": "$Void", "expression": true}, "targetPath": {"value": "$Void"}}, {"expression": {"value": "$isShared", "expression": true}, "targetPath": {"value": "$isShared"}}, {"expression": {"value": "$isSold", "expression": true}, "targetPath": {"value": "$isSold"}}, {"expression": {"value": "$isDemolished", "expression": true}, "targetPath": {"value": "$isDemolished"}}, {"expression": {"value": "$IsGarageOrLockup", "expression": true}, "targetPath": {"value": "$IsGarageOrLockup"}}, {"expression": {"value": "$Aquired", "expression": true}, "targetPath": {"value": "$Aquired"}}, {"expression": {"value": "0", "expression": true}, "targetPath": {"value": "$SHQS"}}, {"expression": {"value": "0", "expression": true}, "targetPath": {"value": "$ExcludeFromDebitRun"}}]}}}, "nullSafeAccess": {"value": true}}, "view_serial": 100}, "class_build_tag": "main22460"}, "9055d232-09f4-40e5-be0c-ff9d92007815": {"class_fqid": "com-snaplogic-snaps-flow-head_1-main27765", "class_id": "com-snaplogic-snaps-flow-head", "class_version": 1, "instance_fqid": "9055d232-09f4-40e5-be0c-ff9d92007815_1", "instance_id": "9055d232-09f4-40e5-be0c-ff9d92007815", "instance_version": 1, "property_map": {"info": {"label": {"value": "Take First"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "numberOfDocs": {"value": 1}, "offset": {"value": 0}}, "view_serial": 100}, "class_build_tag": "main27765"}, "699fde6c-f9f6-4958-8af2-be86f62821be": {"class_fqid": "com-snaplogic-snaps-flow-head_1-main27765", "class_id": "com-snaplogic-snaps-flow-head", "class_version": 1, "instance_fqid": "699fde6c-f9f6-4958-8af2-be86f62821be_1", "instance_id": "699fde6c-f9f6-4958-8af2-be86f62821be", "instance_version": 1, "property_map": {"info": {"label": {"value": "Take First"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "numberOfDocs": {"value": 1}, "offset": {"value": 0}}, "view_serial": 100}, "class_build_tag": "main27765"}, "47d7b8ff-6a0b-48e3-9fba-0604673affaf": {"class_fqid": "com-snaplogic-snaps-sqlserver-execute_1-main21015", "class_id": "com-snaplogic-snaps-sqlserver-execute", "class_version": 1, "instance_fqid": "47d7b8ff-6a0b-48e3-9fba-0604673affaf_1", "instance_id": "47d7b8ff-6a0b-48e3-9fba-0604673affaf", "instance_version": 1, "property_map": {"info": {"label": {"value": "Get Property Exists"}, "notes": {}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"execution_mode": {"value": "Validate & Execute"}, "executable_during_suggest": {"value": false}, "passThrough": {"value": true}, "maxRetryProp": {"value": 0, "expression": false}, "retryIntervalProp": {"value": 1, "expression": false}, "autoCommit": {"value": "Use account setting"}, "sqlStatement": {"value": "\"SELECT count(*) as propExists FROM PropertyStructure_Unit WHERE company = 1 AND ID = '\" + _unitCode + \"'\"", "expression": true}, "ignoreEmptyResult": {"value": false}}, "account": {"account_ref": {"value": {"label": {"value": "KYPERA (Live)", "label": "../shared/KYPERA (Live)", "expression": false}, "ref_class_id": {"value": "com-snaplogic-snap-api-sql-accounts-sqlserverdatabaseaccount"}, "ref_id": {"value": "d1999d56-4b1a-4984-a906-b398bfdae8ea", "expression": false}}}}, "view_serial": 102, "input": {"input102": {"label": {"value": "input0"}, "view_type": {"value": "document"}}}, "output": {"output101": {"label": {"value": "output0"}, "view_type": {"value": "document"}}}}, "class_build_tag": "main21015"}, "1c01c949-c8dd-4709-9614-ef233b76fac7": {"class_fqid": "com-snaplogic-snaps-flow-pipeexec_1-433patches21196", "class_id": "com-snaplogic-snaps-flow-pipeexec", "class_version": 1, "instance_fqid": "1c01c949-c8dd-4709-9614-ef233b76fac7_1", "instance_id": "1c01c949-c8dd-4709-9614-ef233b76fac7", "instance_version": 1, "property_map": {"info": {"label": {"value": "CH01 - Update Kypera Property Street"}, "notes": {}}, "input": {"input0": {"view_type": {"value": "document"}, "label": {"value": "input0"}}}, "output": {"output0": {"view_type": {"value": "document"}, "label": {"value": "output0"}}}, "error": {"error0": {"view_type": {"value": "document"}, "label": {"value": "error0"}}, "error_behavior": {"value": "fail"}}, "settings": {"timeout": {"value": null, "expression": false}, "pipeline": {"expression": false, "value": "CH01 - Update Kypera Property Street"}, "execLabel": {"expression": true, "value": null}, "params": {"value": [{"paramValue": {"expression": true, "value": "_unitCode"}, "paramName": {"value": "unitcode"}}]}, "execution_mode": {"value": "Disabled"}, "snaplexGovernor": {"value": "SNAPLEX_WITH_PATH"}, "retryInterval": {"value": null, "expression": false}, "retryLimit": {"value": null, "expression": false}, "reuse": {"value": false}, "snaplex": {"expression": false}, "executable_during_suggest": {"value": true}, "batchSize": {"value": 1}, "poolSize": {"value": 1, "expression": false}, "loopDocument": {"expression": true}}, "view_serial": 100}, "class_build_tag": "433patches21196"}}, "path_id": "/NCH-prod/D365 - Integration/Properties", "path_snode": "66e810259515898d51ee3bdf"}